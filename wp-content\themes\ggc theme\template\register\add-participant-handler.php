<?php
// AJAX handler for adding new participants
add_action('wp_ajax_add_new_participant', 'handle_add_new_participant');
add_action('wp_ajax_nopriv_add_new_participant', 'handle_add_new_participant');

function handle_add_new_participant() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'add_participant_nonce')) {
        wp_die('Security check failed');
    }

    // Sanitize input data
    $firstName = sanitize_text_field($_POST['firstName']);
    $lastName = sanitize_text_field($_POST['lastName']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);

    // Validate required fields
    if (empty($firstName) || empty($lastName) || empty($email)) {
        wp_send_json_error(array('message' => 'Please fill in all required fields.'));
        return;
    }

    // Validate email format
    if (!is_email($email)) {
        wp_send_json_error(array('message' => 'Please enter a valid email address.'));
        return;
    }

    global $wpdb;
    $registration_table = 'user_registration';
    $payments_table = 'user_payments';

    // Check if email already exists in user_registration
    $existing_user = $wpdb->get_row($wpdb->prepare(
        "SELECT id FROM $registration_table WHERE email = %s",
        $email
    ));

    if ($existing_user) {
        wp_send_json_error(array('message' => 'A user with this email address already exists.'));
        return;
    }

    try {
        // Generate unique client_id using the same format as pricing.php
        $unique_id = date('YmdHis') . '_' . bin2hex(random_bytes(4));

        // Generate username from email address
        $email_parts = explode('@', $email);
        $username = strtolower($email_parts[0]); // Use part before @ symbol
        $base_username = $username;
        $counter = 1;

        // Check if username exists and generate a unique one
        while ($wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $registration_table WHERE username = %s", $username))) {
            $username = $base_username . $counter;
            $counter++;
        }

        // Generate a random password
        $plain_password = wp_generate_password(12, true, true);
        $hashed_password = wp_hash_password($plain_password);

        // Prepare minimal data for user_registration table - only essential fields
        $registration_data = array(
            'username' => $username,
            'client_id' => $unique_id,
            'password' => $hashed_password,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'contact_phone' => $phone,
            'rep_email' => $email
        );

        $registration_format = array(
            '%s', // username
            '%s', // client_id
            '%s', // password
            '%s', // first_name
            '%s', // last_name
            '%s', // contact_phone
            '%s'  // rep_email
        );

        // Insert into user_registration table
        $registration_result = $wpdb->insert($registration_table, $registration_data, $registration_format);

        if ($registration_result === false) {
            wp_send_json_error(array('message' => 'Failed to create user registration. Please try again.'));
            return;
        }

        // Prepare data for user_payments table
        $payment_data = array(
            'client_id' => $unique_id,
            'created_at' => time(),
            'amount' => 0,
            'currency' => 'USD',
            'email' => $email,
            'name' => $firstName . ' ' . $lastName,
            'phone' => $phone,
            'stripe_customer_id' => '',
            'payload' => json_encode(array('manual_creation' => true, 'created_by_admin' => true))
        );

        $payment_format = array('%s', '%d', '%f', '%s', '%s', '%s', '%s', '%s', '%s');

        // Insert into user_payments table
        $payment_result = $wpdb->insert($payments_table, $payment_data, $payment_format);

        if ($payment_result === false) {
            // If payment insertion fails, we should clean up the registration entry
            $wpdb->delete($registration_table, array('client_id' => $unique_id), array('%s'));
            wp_send_json_error(array('message' => 'Failed to create payment record. Please try again.'));
            return;
        }

        // Send welcome email with login credentials
        $subject = 'Welcome to Go Global Conference - Your Login Credentials';
        $message = "Dear {$firstName} {$lastName},\n\n";
        $message .= "Welcome to the Go Global Conference! An account has been created for you.\n\n";
        $message .= "Your login credentials are:\n";
        $message .= "Username: {$username}\n";
        $message .= "Password: {$plain_password}\n\n";
        $message .= "Please log in to complete your registration form at: " . home_url('/ggc-login/') . "\n\n";
        $message .= "If you have any questions, please don't hesitate to contact us.\n\n";
        $message .= "Best regards,\n";
        $message .= "Go Global Conference Team";

        $email_sent = wp_mail($email, $subject, $message);

        if (!$email_sent) {
            // Log the email failure but don't fail the entire process
            error_log("Failed to send welcome email to: " . $email);
        }

        wp_send_json_success(array(
            'message' => 'Participant added successfully! Login credentials have been sent to their email address.',
            'client_id' => $unique_id,
            'username' => $username
        ));

    } catch (Exception $e) {
        wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
    }
}
