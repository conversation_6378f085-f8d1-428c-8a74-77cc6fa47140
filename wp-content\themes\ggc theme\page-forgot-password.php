<?php
/*
Template Name: Custom Forgot Password Page
*/

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

// <PERSON><PERSON> forgot password form submission
if (isset($_POST['reset_submit'])) {
    $user_login = sanitize_text_field($_POST['user_login']);
    
    if (empty($user_login)) {
        $error_message = 'Please enter your username or email address.';
    } else {
        $user_data = get_user_by('email', $user_login);
        if (!$user_data) {
            $user_data = get_user_by('login', $user_login);
        }
        
        if (!$user_data) {
            $error_message = 'No user found with that username or email address.';
        } else {
            // Generate reset key
            $key = get_password_reset_key($user_data);
            if (is_wp_error($key)) {
                $error_message = $key->get_error_message();
            } else {
                // Send reset email
                $message = __('Someone has requested a password reset for the following account:') . "\r\n\r\n";
                $message .= network_home_url('/') . "\r\n\r\n";
                $message .= sprintf(__('Username: %s'), $user_data->user_login) . "\r\n\r\n";
                $message .= __('If this was a mistake, just ignore this email and nothing will happen.') . "\r\n\r\n";
                $message .= __('To reset your password, visit the following address:') . "\r\n\r\n";
                $message .= network_site_url("wp-login.php?action=rp&key=$key&login=" . rawurlencode($user_data->user_login), 'login') . "\r\n";
                
                $title = sprintf(__('[%s] Password Reset'), get_option('blogname'));
                
                if (wp_mail($user_data->user_email, wp_specialchars_decode($title), $message)) {
                    $success_message = 'Check your email for the confirmation link.';
                } else {
                    $error_message = 'The email could not be sent. Please try again later.';
                }
            }
        }
    }
}

get_header(); ?>

<main class="main">
    <!-- breadcrumb -->
    <div class="site-breadcrumb" style="background: url(<?php echo get_template_directory_uri(); ?>/assets/img/breadcrumb/01.jpg)">
        <div class="container">
            <h2 class="breadcrumb-title">Forgot Password</h2>
            <ul class="breadcrumb-menu">
                <li><a href="<?php echo home_url(); ?>">Home</a></li>
                <li><a href="<?php echo home_url('/login/'); ?>">Login</a></li>
                <li class="active">Forgot Password</li>
            </ul>
        </div>
    </div>
    <!-- breadcrumb end -->

    <!-- forgot password area -->
    <div class="login-area py-120">
        <div class="container">
            <div class="col-md-5 mx-auto">
                <div class="login-form">
                    <div class="login-header">
                        <?php if (has_custom_logo()) : ?>
                            <?php the_custom_logo(); ?>
                        <?php else : ?>
                            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/logo/logo.png" alt="<?php bloginfo('name'); ?>">
                        <?php endif; ?>
                        <p>Reset your password</p>
                    </div>

                    <?php if (isset($error_message)) : ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="far fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($success_message)) : ?>
                        <div class="alert alert-success" role="alert">
                            <i class="far fa-check-circle"></i> <?php echo $success_message; ?>
                        </div>
                    <?php else : ?>
                        <div class="alert alert-info" role="alert">
                            <i class="far fa-info-circle"></i> 
                            Please enter your username or email address. You will receive an email message with instructions on how to reset your password.
                        </div>

                        <form method="post" action="">
                            <?php wp_nonce_field('custom_forgot_password_nonce', 'forgot_password_nonce'); ?>
                            
                            <div class="input-group">
                                <span class="input-group-text"><i class="far fa-envelope"></i></span>
                                <input type="text" name="user_login" class="form-control" placeholder="Username or Email" required value="<?php echo isset($_POST['user_login']) ? esc_attr($_POST['user_login']) : ''; ?>">
                            </div>
                            
                            <div class="d-flex align-items-center">
                                <button type="submit" name="reset_submit" class="theme-btn">
                                    <span class="far fa-paper-plane"></span> Get New Password
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <div class="login-footer">
                        <p class="mt-20">
                            <a href="<?php echo home_url('/login/'); ?>">← Back to Login</a>
                        </p>
                        <?php if (get_option('users_can_register')) : ?>
                            <p>Don't have an account? <a href="<?php echo home_url('/register/'); ?>">Register.</a></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- forgot password area end -->
</main>

<?php get_footer(); ?>
