<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && check_admin_referer('register_user_nonce')) {
    // Database connection using WordPress globals
    global $wpdb;
    $table_name = 'user_registration';

    try {
        // Handle file uploads
        $upload_dir = wp_upload_dir();
        $business_profile = '';
        $company_logo = '';
        $headshot = '';
        $max_size = 5 * 1024 * 1024; // 5MB

        // Validate required files
        if (empty($_FILES['business_profile']['name']) || empty($_FILES['company_logo']['name']) || empty($_FILES['headshot']['name'])) {
            throw new Exception('All files (Business Profile, Company Logo, and Headshot) are required.');
        }

        // Handle business profile upload
        if (!empty($_FILES['business_profile']['name'])) {
            $business_profile_file = $_FILES['business_profile'];

            // Validate file size
            if ($business_profile_file['size'] > $max_size) {
                throw new Exception('Business Profile file size must not exceed 5MB.');
            }

            // Validate file type
            $file_type = strtolower(pathinfo($business_profile_file['name'], PATHINFO_EXTENSION));
            if ($file_type !== 'pdf') {
                throw new Exception('Business Profile must be a PDF file.');
            }

            $business_profile_path = $upload_dir['path'] . '/' . sanitize_file_name($business_profile_file['name']);
            if (move_uploaded_file($business_profile_file['tmp_name'], $business_profile_path)) {
                $business_profile = $upload_dir['url'] . '/' . sanitize_file_name($business_profile_file['name']);
            } else {
                throw new Exception('Failed to upload Business Profile.');
            }
        }

        // Handle company logo upload
        if (!empty($_FILES['company_logo']['name'])) {
            $company_logo_file = $_FILES['company_logo'];

            // Validate file size
            if ($company_logo_file['size'] > $max_size) {
                throw new Exception('Company Logo file size must not exceed 5MB.');
            }

            // Validate file type
            $file_type = strtolower(pathinfo($company_logo_file['name'], PATHINFO_EXTENSION));
            if (!in_array($file_type, ['jpg', 'jpeg', 'png'])) {
                throw new Exception('Company Logo must be a JPG or PNG file.');
            }

            $company_logo_path = $upload_dir['path'] . '/' . sanitize_file_name($company_logo_file['name']);
            if (move_uploaded_file($company_logo_file['tmp_name'], $company_logo_path)) {
                $company_logo = $upload_dir['url'] . '/' . sanitize_file_name($company_logo_file['name']);
            } else {
                throw new Exception('Failed to upload Company Logo.');
            }
        }

        // Handle headshot upload
        if (!empty($_FILES['headshot']['name'])) {
            $headshot_file = $_FILES['headshot'];

            // Validate file size
            if ($headshot_file['size'] > $max_size) {
                throw new Exception('Headshot file size must not exceed 5MB.');
            }

            // Validate file type
            $file_type = strtolower(pathinfo($headshot_file['name'], PATHINFO_EXTENSION));
            if (!in_array($file_type, ['jpg', 'jpeg', 'png'])) {
                throw new Exception('Headshot must be a JPG or PNG file.');
            }

            $headshot_path = $upload_dir['path'] . '/' . sanitize_file_name($headshot_file['name']);
            if (move_uploaded_file($headshot_file['tmp_name'], $headshot_path)) {
                $headshot = $upload_dir['url'] . '/' . sanitize_file_name($headshot_file['name']);
            } else {
                throw new Exception('Failed to upload Headshot.');
            }
        }
        // Sanitize and collect form data
        $participated_before = isset($_POST['b2bMatchParticipation']) ? ($_POST['b2bMatchParticipation'] === 'Yes' ? 1 : 0) : null;
        $first_name = sanitize_text_field($_POST['firstName']);
        $last_name = sanitize_text_field($_POST['lastName']);
        $contact_phone = sanitize_text_field($_POST['phoneNumber']);
        $preferred_language = sanitize_text_field($_POST['preferredLanguage']);
        $country = sanitize_text_field($_POST['country']);
        $city_state_zip = sanitize_text_field($_POST['address']);
        $company_name = sanitize_text_field($_POST['companyname']);
        $company_website = esc_url_raw($_POST['companyWebsite']);
        $linkedin_url = esc_url_raw($_POST['linkedIn']);
        $social_media_links = sanitize_text_field($_POST['socialMedia']);
        $business_description = sanitize_textarea_field($_POST['describeBusiness']);
        $years_in_operation = absint($_POST['yearsBusiness']);
        $number_of_employees = absint($_POST['numEmployees']);
        $annual_revenue = sanitize_text_field($_POST['annualRevenue']);
        $rep_name = sanitize_text_field($_POST['repName']);
        $rep_title = sanitize_text_field($_POST['repTitle']);
        $rep_email = sanitize_email($_POST['repEmail']);
        $rep_phone = sanitize_text_field($_POST['repPhone']);

        // Handle conference days
        $attending_days = isset($_POST['conference-days-participating']) ? $_POST['conference-days-participating'] : [];
        $attending_day1 = in_array('Day 1 - Welcome Reception (6:00 PM - 10:00 PM)', $attending_days) ? 1 : 0;
        $attending_day2 = in_array('Day 2 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)', $attending_days) ? 1 : 0;
        $attending_day3 = in_array('Day 3 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)', $attending_days) ? 1 : 0;
        $attending_day4_visits = in_array('Day 4 - Site Visits (International Attendees ONLY)', $attending_days) ? 1 : 0;
        $attending_day4_closing = in_array('Day 4 - Closing Reception (5:00 PM - 9:00 PM)', $attending_days) ? 1 : 0;

        // Generate username and password
        $username = strtolower(substr($first_name, 0, 1) . $last_name);
        $base_username = $username;
        $counter = 1;

        // Check if username exists and generate a unique one
        while ($wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE username = %s", $username))) {
            $username = $base_username . $counter;
            $counter++;
        }

        $password = wp_hash_password(wp_generate_password(12, true, true));

        // Prepare data for insertion
        $data = array(
            'username' => $username,
            'password' => $password,
            'participated_before' => $participated_before,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'contact_phone' => $contact_phone,
            'preferred_language' => $preferred_language,
            'country' => $country,
            'city_state_zip' => $city_state_zip,
            'company_name' => $company_name,
            'company_website' => $company_website,
            'linkedin_url' => $linkedin_url,
            'social_media_links' => $social_media_links,
            'business_description' => $business_description,
            'years_in_operation' => $years_in_operation,
            'number_of_employees' => $number_of_employees,
            'annual_revenue' => $annual_revenue,
            'rep_name' => $rep_name,
            'rep_title' => $rep_title,
            'rep_email' => $rep_email,
            'rep_phone' => $rep_phone,
            'attending_day1' => $attending_day1,
            'attending_day2' => $attending_day2,
            'attending_day3' => $attending_day3,
            'attending_day4_visits' => $attending_day4_visits,
            'attending_day4_closing' => $attending_day4_closing,
            'business_profile_upload' => $business_profile,
            'company_logo_upload' => $company_logo,
            'headshot_upload' => $headshot,
            'consent_store_data' => 1,
            'confirm_participation' => 1,
            'share_with_partners' => 1,
            'post_event_contact' => 1,
            'agree_terms' => 1
        );

        $format = array(
            '%s',
            '%s',
            '%d',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%d',
            '%d',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%d',
            '%d',
            '%d',
            '%d',
            '%d'
        );

        // Insert the data
        $result = $wpdb->insert($table_name, $data, $format);

        if ($result !== false) {
            $plain_password = substr($password, 0, 12); // Get the first 12 characters of the hashed password
            echo "<div class='alert alert-success'>
                    Registration successful!<br>
                    Your login credentials:<br>
                    Username: " . esc_html($username) . "<br>
                    Password: " . esc_html($plain_password) . "<br>
                    Please save these credentials.
                  </div>";
        } else {
            echo "<div class='alert alert-danger'>Error: Registration failed. Please try again.</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . esc_html($e->getMessage()) . "</div>";
    }

    get_template_part('template/register/submit_mautic');
}
