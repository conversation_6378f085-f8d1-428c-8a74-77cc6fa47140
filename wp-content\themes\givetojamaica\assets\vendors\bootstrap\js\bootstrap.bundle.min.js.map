{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/base-component.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/scrollbar.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "isRTL", "dir", "defineJQueryPlugin", "name", "plugin", "callback", "$", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "elementMap", "Map", "Data", "set", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "remove", "delete", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "add<PERSON><PERSON><PERSON>", "oneOff", "handlers", "previousFn", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "[object Object]", "VERSION", "<PERSON><PERSON>", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "closest", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "each", "data", "alertInstance", "handle<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "start", "pointerType", "touches", "clientX", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "indexOf", "_getItemByOrder", "activeElement", "isNext", "isPrev", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "id", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "bottom", "right", "basePlacements", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "styles", "assign", "effect", "_ref2", "initialStyles", "popper", "options", "strategy", "margin", "arrow", "reference", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "width", "height", "x", "y", "getLayoutRect", "clientRect", "offsetWidth", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "max", "min", "round", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "_ref3", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "_ref3$x", "_ref3$y", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "computeStyles$1", "_ref4", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "data-popper-placement", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "pageXOffset", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "listScrollParents", "list", "_element$ownerDocumen", "getScrollParent", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "getVariation", "computeOffsets", "variation", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "referenceElement", "clippingClientRect", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$2", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "data-popper-reference-hidden", "data-popper-escaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_min", "_max", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "DEFAULT_OPTIONS", "modifiers", "areValidElements", "_len", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "cleanupModifierEffects", "merged", "map", "visited", "result", "modifier", "dep", "depModifier", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "undefined", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "REGEXP_KEYDOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "_getPopperConfig", "isDisplayStatic", "focus", "_getPlacement", "parentDropdown", "isEnd", "getPropertyValue", "_getOffset", "popperData", "defaultBsPopperConfig", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "<PERSON><PERSON><PERSON>", "stopPropagation", "click", "items", "dataApiKeydownHandler", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_isAnimated", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "isAnimated", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "modalTransitionDuration", "paddingLeft", "paddingRight", "innerWidth", "_getScrollbarWidth", "_setElementAttributes", "calculatedValue", "styleProp", "actualValue", "_resetElementAttributes", "scrollDiv", "scrollbarWidth", "getWidth", "documentWidth", "removeProperty", "<PERSON><PERSON><PERSON>", "scrollBarHide", "_enforceFocusOnElement", "blur", "allReadyOpen", "el", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "*", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "NAME", "EVENT_KEY", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "Error", "isWithContent", "shadowRoot", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "updateAttachment", "dataKey", "_getDelegateConfig", "_handlePopperPlacementChange", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;0OAOA,MAmBMA,EAASC,IACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAWH,EAAQE,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAW,IAAMA,EAASG,MAAM,KAAK,IAGvCL,EAAWE,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKJ,SAASY,cAAcR,GAAYA,EAGrC,MAGHS,EAAyBV,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWJ,SAASY,cAAcR,GAAY,MAGjDU,EAAmCX,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIY,mBAAEA,EAAFC,gBAAsBA,GAAoBC,OAAOC,iBAAiBf,GAEtE,MAAMgB,EAA0BC,OAAOC,WAAWN,GAC5CO,EAAuBF,OAAOC,WAAWL,GAG/C,OAAKG,GAA4BG,GAKjCP,EAAqBA,EAAmBN,MAAM,KAAK,GACnDO,EAAkBA,EAAgBP,MAAM,KAAK,GArFf,KAuFtBW,OAAOC,WAAWN,GAAsBK,OAAOC,WAAWL,KAPzD,GAULO,EAAuBpB,IAC3BA,EAAQqB,cAAc,IAAIC,MA1FL,mBA6FjBC,EAAYC,IAAQA,EAAI,IAAMA,GAAKC,SAEnCC,EAAuB,CAAC1B,EAAS2B,KACrC,IAAIC,GAAS,EACb,MACMC,EAAmBF,EADD,EAQxB3B,EAAQ8B,iBAzGa,iBAoGrB,SAASC,IACPH,GAAS,EACT5B,EAAQgC,oBAtGW,gBAsGyBD,MAI9CE,WAAW,KACJL,GACHR,EAAqBpB,IAEtB6B,IAGCK,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GAAS,UAjH5CnB,OADSA,EAkHsDmB,GAhHzD,GAAEnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cALxCxB,IAAAA,EAoHX,IAAK,IAAIyB,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,UACLhB,EAAciB,cAAhB,KACA,WAAUX,qBAA4BG,MACtC,sBAAqBF,UAMxBW,EAAYrD,IAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQsD,OAAStD,EAAQuD,YAAcvD,EAAQuD,WAAWD,MAAO,CACnE,MAAME,EAAezC,iBAAiBf,GAChCyD,EAAkB1C,iBAAiBf,EAAQuD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,EAAa5D,IACZA,GAAWA,EAAQyB,WAAaoC,KAAKC,gBAItC9D,EAAQ+D,UAAUC,SAAS,mBAIC,IAArBhE,EAAQiE,SACVjE,EAAQiE,SAGVjE,EAAQkE,aAAa,aAAoD,UAArClE,EAAQE,aAAa,aAG5DiE,EAAiBnE,IACrB,IAAKH,SAASuE,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrE,EAAQsE,YAA4B,CAC7C,MAAMC,EAAOvE,EAAQsE,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIvE,aAAmBwE,WACdxE,EAIJA,EAAQuD,WAINY,EAAenE,EAAQuD,YAHrB,MAMLkB,EAAO,IAAM,aAEbC,EAAS1E,GAAWA,EAAQ2E,aAE5BC,EAAY,KAChB,MAAMC,OAAEA,GAAW/D,OAEnB,OAAI+D,IAAWhF,SAASiF,KAAKZ,aAAa,qBACjCW,EAGF,MAWHE,EAAQ,IAAuC,QAAjClF,SAASuE,gBAAgBY,IAEvCC,EAAqB,CAACC,EAAMC,KAVPC,IAAAA,EAAAA,EAWN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAqBD,EAAEE,GAAGL,GAChCG,EAAEE,GAAGL,GAAQC,EAAOK,gBACpBH,EAAEE,GAAGL,GAAMO,YAAcN,EACzBE,EAAEE,GAAGL,GAAMQ,WAAa,KACtBL,EAAEE,GAAGL,GAAQI,EACNH,EAAOK,mBAnBQ,YAAxB3F,SAAS8F,WACX9F,SAASiC,iBAAiB,mBAAoBsD,GAE9CA,KCvMEQ,EAAa,IAAIC,IAEvB,IAAAC,EAAe,CACbC,IAAI/F,EAASgG,EAAKC,GACXL,EAAWM,IAAIlG,IAClB4F,EAAWG,IAAI/F,EAAS,IAAI6F,KAG9B,MAAMM,EAAcP,EAAWQ,IAAIpG,GAI9BmG,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYJ,IAAIC,EAAKC,GAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,EAAY5D,QAAQ,QAOhI6D,IAAG,CAACpG,EAASgG,IACPJ,EAAWM,IAAIlG,IACV4F,EAAWQ,IAAIpG,GAASoG,IAAIJ,IAG9B,KAGTU,OAAO1G,EAASgG,GACd,IAAKJ,EAAWM,IAAIlG,GAClB,OAGF,MAAMmG,EAAcP,EAAWQ,IAAIpG,GAEnCmG,EAAYQ,OAAOX,GAGM,IAArBG,EAAYE,MACdT,EAAWe,OAAO3G,KCtCxB,MAAM4G,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAYtH,EAASuH,GAC5B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiBhH,EAAQgH,UAAYA,IAGjE,SAASQ,EAASxH,GAChB,MAAMuH,EAAMD,EAAYtH,GAKxB,OAHAA,EAAQgH,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,GAuCvB,SAASE,EAAYC,EAAQC,EAASC,EAAqB,MACzD,MAAMC,EAAevF,OAAOC,KAAKmF,GAEjC,IAAK,IAAII,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,MAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,MAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGpD,IAAIY,EAAYH,EAAkBI,QAAQ3B,EAAgB,IAC1D,MAAM4B,EAASxB,EAAasB,GAY5B,OAVIE,IACFF,EAAYE,GAGGrB,EAAalB,IAAIqC,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW1I,EAASoI,EAAmBT,EAASU,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmCpI,EAC5C,OAGG2H,IACHA,EAAUU,EACVA,EAAe,MAGjB,MAAOC,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBT,EAASU,GACvFX,EAASF,EAASxH,GAClB4I,EAAWlB,EAAOa,KAAeb,EAAOa,GAAa,IACrDM,EAAapB,EAAYmB,EAAUV,EAAiBI,EAAaX,EAAU,MAEjF,GAAIkB,EAGF,YAFAA,EAAWF,OAASE,EAAWF,QAAUA,GAK3C,MAAMpB,EAAMD,EAAYY,EAAiBE,EAAkBI,QAAQ5B,EAAgB,KAC7ErB,EAAK+C,EAjFb,SAAoCtI,EAASC,EAAUsF,GACrD,OAAO,SAASoC,EAAQM,GACtB,MAAMa,EAAc9I,EAAQ+I,iBAAiB9I,GAE7C,IAAK,IAAI+I,OAAEA,GAAWf,EAAOe,GAAUA,IAAWC,KAAMD,EAASA,EAAOzF,WACtE,IAAK,IAAIuE,EAAIgB,EAAYd,OAAQF,KAC/B,GAAIgB,EAAYhB,KAAOkB,EAQrB,OAPAf,EAAMiB,eAAiBF,EAEnBrB,EAAQgB,QAEVQ,EAAaC,IAAIpJ,EAASiI,EAAMoB,KAAM9D,GAGjCA,EAAG+D,MAAMN,EAAQ,CAACf,IAM/B,OAAO,MA8DPsB,CAA2BvJ,EAAS2H,EAASU,GA9FjD,SAA0BrI,EAASuF,GACjC,OAAO,SAASoC,EAAQM,GAOtB,OANAA,EAAMiB,eAAiBlJ,EAEnB2H,EAAQgB,QACVQ,EAAaC,IAAIpJ,EAASiI,EAAMoB,KAAM9D,GAGjCA,EAAG+D,MAAMtJ,EAAS,CAACiI,KAuF1BuB,CAAiBxJ,EAAS2H,GAE5BpC,EAAGqC,mBAAqBU,EAAaX,EAAU,KAC/CpC,EAAG2C,gBAAkBA,EACrB3C,EAAGoD,OAASA,EACZpD,EAAGyB,SAAWO,EACdqB,EAASrB,GAAOhC,EAEhBvF,EAAQ8B,iBAAiByG,EAAWhD,EAAI+C,GAG1C,SAASmB,EAAczJ,EAAS0H,EAAQa,EAAWZ,EAASC,GAC1D,MAAMrC,EAAKkC,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CrC,IAILvF,EAAQgC,oBAAoBuG,EAAWhD,EAAImE,QAAQ9B,WAC5CF,EAAOa,GAAWhD,EAAGyB,WAe9B,MAAMmC,EAAe,CACnBQ,GAAG3J,EAASiI,EAAON,EAASU,GAC1BK,EAAW1I,EAASiI,EAAON,EAASU,GAAc,IAGpDuB,IAAI5J,EAASiI,EAAON,EAASU,GAC3BK,EAAW1I,EAASiI,EAAON,EAASU,GAAc,IAGpDe,IAAIpJ,EAASoI,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,IAAmCpI,EAC5C,OAGF,MAAOsI,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBT,EAASU,GACvFwB,EAActB,IAAcH,EAC5BV,EAASF,EAASxH,GAClB8J,EAAc1B,EAAkB/H,WAAW,KAEjD,QAA+B,IAApB6H,EAAiC,CAE1C,IAAKR,IAAWA,EAAOa,GACrB,OAIF,YADAkB,EAAczJ,EAAS0H,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,MAIhFmC,GACFxH,OAAOC,KAAKmF,GAAQlF,QAAQuH,KA1ClC,SAAkC/J,EAAS0H,EAAQa,EAAWyB,GAC5D,MAAMC,EAAoBvC,EAAOa,IAAc,GAE/CjG,OAAOC,KAAK0H,GAAmBzH,QAAQ0H,IACrC,GAAIA,EAAW9J,SAAS4J,GAAY,CAClC,MAAM/B,EAAQgC,EAAkBC,GAEhCT,EAAczJ,EAAS0H,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,uBAoCrEuC,CAAyBnK,EAAS0H,EAAQqC,EAAc3B,EAAkBgC,MAAM,MAIpF,MAAMH,EAAoBvC,EAAOa,IAAc,GAC/CjG,OAAOC,KAAK0H,GAAmBzH,QAAQ6H,IACrC,MAAMH,EAAaG,EAAY7B,QAAQ1B,EAAe,IAEtD,IAAK+C,GAAezB,EAAkBhI,SAAS8J,GAAa,CAC1D,MAAMjC,EAAQgC,EAAkBI,GAEhCZ,EAAczJ,EAAS0H,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAK7E0C,QAAQtK,EAASiI,EAAOsC,GACtB,GAAqB,iBAAVtC,IAAuBjI,EAChC,OAAO,KAGT,MAAMqF,EAAIT,IACJ2D,EAAYN,EAAMO,QAAQ3B,EAAgB,IAC1CgD,EAAc5B,IAAUM,EACxBiC,EAAWpD,EAAalB,IAAIqC,GAElC,IAAIkC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAexE,IACjBoF,EAAcpF,EAAE/D,MAAM2G,EAAOsC,GAE7BlF,EAAErF,GAASsK,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAMhL,SAASoL,YAAY,cAC3BJ,EAAIK,UAAU3C,EAAWmC,GAAS,IAElCG,EAAM,IAAIM,YAAYlD,EAAO,CAC3ByC,QAAAA,EACAU,YAAY,SAKI,IAATb,GACTjI,OAAOC,KAAKgI,GAAM/H,QAAQwD,IACxB1D,OAAO+I,eAAeR,EAAK7E,EAAK,CAC9BI,IAAG,IACMmE,EAAKvE,OAMhB4E,GACFC,EAAIS,iBAGFX,GACF3K,EAAQqB,cAAcwJ,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYa,iBAGPT,ICrTX,MAAMU,EACJC,YAAYxL,IACVA,EAA6B,iBAAZA,EAAuBH,SAASY,cAAcT,GAAWA,KAM1EiJ,KAAKwC,SAAWzL,EAChB8F,EAAKC,IAAIkD,KAAKwC,SAAUxC,KAAKuC,YAAYE,SAAUzC,OAGrD0C,UACE7F,EAAKY,OAAOuC,KAAKwC,SAAUxC,KAAKuC,YAAYE,UAC5CzC,KAAKwC,SAAW,KAKAG,mBAAC5L,GACjB,OAAO8F,EAAKM,IAAIpG,EAASiJ,KAAKyC,UAGdG,qBAChB,MA1BY,eC6BhB,MAAMC,UAAcP,EAGCG,sBACjB,MAxBa,WA6BfK,MAAM/L,GACJ,MAAMgM,EAAchM,EAAUiJ,KAAKgD,gBAAgBjM,GAAWiJ,KAAKwC,SAC7DS,EAAcjD,KAAKkD,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYtB,kBAIxC3B,KAAKmD,eAAeJ,GAKtBC,gBAAgBjM,GACd,OAAOU,EAAuBV,IAAYA,EAAQqM,QAAS,UAG7DF,mBAAmBnM,GACjB,OAAOmJ,EAAamB,QAAQtK,EAzCX,kBA4CnBoM,eAAepM,GAGb,GAFAA,EAAQ+D,UAAU2C,OAvCE,SAyCf1G,EAAQ+D,UAAUC,SA1CH,QA4ClB,YADAiF,KAAKqD,gBAAgBtM,GAIvB,MAAMY,EAAqBD,EAAiCX,GAE5DmJ,EAAaS,IAAI5J,EAAS,gBAAiB,IAAMiJ,KAAKqD,gBAAgBtM,IACtE0B,EAAqB1B,EAASY,GAGhC0L,gBAAgBtM,GACVA,EAAQuD,YACVvD,EAAQuD,WAAWgJ,YAAYvM,GAGjCmJ,EAAamB,QAAQtK,EA9DH,mBAmEE4L,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KA5ET,YA8ENwD,IACHA,EAAO,IAAIX,EAAM7C,OAGJ,UAAX7G,GACFqK,EAAKrK,GAAQ6G,SAKC2C,qBAACc,GACnB,OAAO,SAAUzE,GACXA,GACFA,EAAMqD,iBAGRoB,EAAcX,MAAM9C,QAW1BE,EAAaQ,GAAG9J,SAjGc,0BAJL,4BAqGyCiM,EAAMa,cAAc,IAAIb,IAS1F7G,EAnHa,QAmHY6G,GCvGzB,MAAMc,UAAerB,EAGAG,sBACjB,MApBa,YAyBfmB,SAEE5D,KAAKwC,SAASqB,aAAa,eAAgB7D,KAAKwC,SAAS1H,UAAU8I,OAvB7C,WA4BFjB,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KAlCT,aAoCNwD,IACHA,EAAO,IAAIG,EAAO3D,OAGL,WAAX7G,GACFqK,EAAKrK,SCrDb,SAAS2K,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ/L,OAAO+L,GAAKnK,WACf5B,OAAO+L,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBjH,GACxB,OAAOA,EAAIwC,QAAQ,SAAU0E,GAAQ,IAAGA,EAAIlK,eD4C9CmG,EAAaQ,GAAG9J,SA7Cc,2BAFD,4BA+CyCoI,IACpEA,EAAMqD,iBAEN,MAAM6B,EAASlF,EAAMe,OAAOqD,QAlDD,6BAoD3B,IAAII,EAAO3G,EAAKM,IAAI+G,EA1DL,aA2DVV,IACHA,EAAO,IAAIG,EAAOO,IAGpBV,EAAKI,WAUP5H,EA1Ea,SA0EY2H,GC7DzB,MAAMQ,EAAc,CAClBC,iBAAiBrN,EAASgG,EAAKrD,GAC7B3C,EAAQ8M,aAAc,WAAUG,EAAiBjH,GAAQrD,IAG3D2K,oBAAoBtN,EAASgG,GAC3BhG,EAAQuN,gBAAiB,WAAUN,EAAiBjH,KAGtDwH,kBAAkBxN,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMyN,EAAa,GAUnB,OARAnL,OAAOC,KAAKvC,EAAQ0N,SACjBC,OAAO3H,GAAOA,EAAI3F,WAAW,OAC7BmC,QAAQwD,IACP,IAAI4H,EAAU5H,EAAIwC,QAAQ,MAAO,IACjCoF,EAAUA,EAAQC,OAAO,GAAG7K,cAAgB4K,EAAQxD,MAAM,EAAGwD,EAAQ5F,QACrEyF,EAAWG,GAAWb,EAAc/M,EAAQ0N,QAAQ1H,MAGjDyH,GAGTK,iBAAgB,CAAC9N,EAASgG,IACjB+G,EAAc/M,EAAQE,aAAc,WAAU+M,EAAiBjH,KAGxE+H,OAAO/N,GACL,MAAMgO,EAAOhO,EAAQiO,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMrO,SAASiF,KAAKqJ,UAC9BC,KAAMJ,EAAKI,KAAOvO,SAASiF,KAAKuJ,aAIpCC,SAAStO,IACA,CACLkO,IAAKlO,EAAQuO,UACbH,KAAMpO,EAAQwO,cC3DdC,EAAiB,CACrBC,KAAI,CAACzO,EAAUD,EAAUH,SAASuE,kBACzB,GAAGuK,UAAUC,QAAQC,UAAU9F,iBAAiBjG,KAAK9C,EAASC,IAGvE6O,QAAO,CAAC7O,EAAUD,EAAUH,SAASuE,kBAC5BwK,QAAQC,UAAUpO,cAAcqC,KAAK9C,EAASC,GAGvD8O,SAAQ,CAAC/O,EAASC,IACT,GAAG0O,UAAU3O,EAAQ+O,UACzBpB,OAAOqB,GAASA,EAAMC,QAAQhP,IAGnCiP,QAAQlP,EAASC,GACf,MAAMiP,EAAU,GAEhB,IAAIC,EAAWnP,EAAQuD,WAEvB,KAAO4L,GAAYA,EAAS1N,WAAaoC,KAAKC,cArBhC,IAqBgDqL,EAAS1N,UACjE0N,EAASF,QAAQhP,IACnBiP,EAAQE,KAAKD,GAGfA,EAAWA,EAAS5L,WAGtB,OAAO2L,GAGTG,KAAKrP,EAASC,GACZ,IAAIqP,EAAWtP,EAAQuP,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASL,QAAQhP,GACnB,MAAO,CAACqP,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAKxP,EAASC,GACZ,IAAIuP,EAAOxP,EAAQyP,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKP,QAAQhP,GACf,MAAO,CAACuP,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC9BLC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QA2CxB,MAAMC,UAAiB/E,EACrBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAKuH,OAAS,KACdvH,KAAKwH,UAAY,KACjBxH,KAAKyH,eAAiB,KACtBzH,KAAK0H,WAAY,EACjB1H,KAAK2H,YAAa,EAClB3H,KAAK4H,aAAe,KACpB5H,KAAK6H,YAAc,EACnB7H,KAAK8H,YAAc,EAEnB9H,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAKiI,mBAAqBzC,EAAeK,QA3BjB,uBA2B8C7F,KAAKwC,UAC3ExC,KAAKkI,gBAAkB,iBAAkBtR,SAASuE,iBAAmBgN,UAAUC,eAAiB,EAChGpI,KAAKqI,cAAgB5H,QAAQ5I,OAAOyQ,cAEpCtI,KAAKuI,qBAKW9B,qBAChB,OAAOA,EAGUhE,sBACjB,MArGa,cA0Gf8D,OACOvG,KAAK2H,YACR3H,KAAKwI,OAAOvB,GAIhBwB,mBAGO7R,SAAS8R,QAAUtO,EAAU4F,KAAKwC,WACrCxC,KAAKuG,OAITH,OACOpG,KAAK2H,YACR3H,KAAKwI,OAAOtB,GAIhBL,MAAM7H,GACCA,IACHgB,KAAK0H,WAAY,GAGflC,EAAeK,QAxEI,2CAwEwB7F,KAAKwC,YAClDrK,EAAqB6H,KAAKwC,UAC1BxC,KAAK2I,OAAM,IAGbC,cAAc5I,KAAKwH,WACnBxH,KAAKwH,UAAY,KAGnBmB,MAAM3J,GACCA,IACHgB,KAAK0H,WAAY,GAGf1H,KAAKwH,YACPoB,cAAc5I,KAAKwH,WACnBxH,KAAKwH,UAAY,MAGfxH,KAAK+H,SAAW/H,KAAK+H,QAAQrB,WAAa1G,KAAK0H,YACjD1H,KAAK6I,kBAEL7I,KAAKwH,UAAYsB,aACdlS,SAASmS,gBAAkB/I,KAAKyI,gBAAkBzI,KAAKuG,MAAMyC,KAAKhJ,MACnEA,KAAK+H,QAAQrB,WAKnBuC,GAAGC,GACDlJ,KAAKyH,eAAiBjC,EAAeK,QAzGZ,wBAyG0C7F,KAAKwC,UACxE,MAAM2G,EAAcnJ,KAAKoJ,cAAcpJ,KAAKyH,gBAE5C,GAAIyB,EAAQlJ,KAAKuH,OAAOxI,OAAS,GAAKmK,EAAQ,EAC5C,OAGF,GAAIlJ,KAAK2H,WAEP,YADAzH,EAAaS,IAAIX,KAAKwC,SAxIR,mBAwI8B,IAAMxC,KAAKiJ,GAAGC,IAI5D,GAAIC,IAAgBD,EAGlB,OAFAlJ,KAAK6G,aACL7G,KAAK2I,QAIP,MAAMU,EAAQH,EAAQC,EACpBlC,EACAC,EAEFlH,KAAKwI,OAAOa,EAAOrJ,KAAKuH,OAAO2B,IAGjCxG,UACExC,EAAaC,IAAIH,KAAKwC,SA1LP,gBA4LfxC,KAAKuH,OAAS,KACdvH,KAAK+H,QAAU,KACf/H,KAAKwH,UAAY,KACjBxH,KAAK0H,UAAY,KACjB1H,KAAK2H,WAAa,KAClB3H,KAAKyH,eAAiB,KACtBzH,KAAKiI,mBAAqB,KAE1BX,MAAM5E,UAKRsF,WAAW7O,GAMT,OALAA,EAAS,IACJsN,KACAtN,GAELF,EAhNS,WAgNaE,EAAQ6N,GACvB7N,EAGTmQ,eACE,MAAMC,EAAY9S,KAAK+S,IAAIxJ,KAAK8H,aAEhC,GAAIyB,GA/MgB,GAgNlB,OAGF,MAAME,EAAYF,EAAYvJ,KAAK8H,YAEnC9H,KAAK8H,YAAc,EAEd2B,GAILzJ,KAAKwI,OAAOiB,EAAY,EAAIrC,EAAkBD,GAGhDoB,qBACMvI,KAAK+H,QAAQpB,UACfzG,EAAaQ,GAAGV,KAAKwC,SArMJ,sBAqM6BxD,GAASgB,KAAK0J,SAAS1K,IAG5C,UAAvBgB,KAAK+H,QAAQlB,QACf3G,EAAaQ,GAAGV,KAAKwC,SAxMD,yBAwM6BxD,GAASgB,KAAK6G,MAAM7H,IACrEkB,EAAaQ,GAAGV,KAAKwC,SAxMD,yBAwM6BxD,GAASgB,KAAK2I,MAAM3J,KAGnEgB,KAAK+H,QAAQhB,OAAS/G,KAAKkI,iBAC7BlI,KAAK2J,0BAITA,0BACE,MAAMC,EAAQ5K,KACRgB,KAAKqI,eApLU,QAoLQrJ,EAAM6K,aArLZ,UAqLgD7K,EAAM6K,YAE/D7J,KAAKqI,gBACfrI,KAAK6H,YAAc7I,EAAM8K,QAAQ,GAAGC,SAFpC/J,KAAK6H,YAAc7I,EAAM+K,SAMvBC,EAAOhL,IAEXgB,KAAK8H,YAAc9I,EAAM8K,SAAW9K,EAAM8K,QAAQ/K,OAAS,EACzD,EACAC,EAAM8K,QAAQ,GAAGC,QAAU/J,KAAK6H,aAG9BoC,EAAMjL,KACNgB,KAAKqI,eAnMU,QAmMQrJ,EAAM6K,aApMZ,UAoMgD7K,EAAM6K,cACzE7J,KAAK8H,YAAc9I,EAAM+K,QAAU/J,KAAK6H,aAG1C7H,KAAKsJ,eACsB,UAAvBtJ,KAAK+H,QAAQlB,QASf7G,KAAK6G,QACD7G,KAAK4H,cACPsC,aAAalK,KAAK4H,cAGpB5H,KAAK4H,aAAe5O,WAAWgG,GAASgB,KAAK2I,MAAM3J,GAlR5B,IAkR6DgB,KAAK+H,QAAQrB,YAIrGlB,EAAeC,KAlOO,qBAkOiBzF,KAAKwC,UAAUjJ,QAAQ4Q,IAC5DjK,EAAaQ,GAAGyJ,EAnPI,wBAmPuBC,GAAKA,EAAE/H,oBAGhDrC,KAAKqI,eACPnI,EAAaQ,GAAGV,KAAKwC,SAzPA,0BAyP6BxD,GAAS4K,EAAM5K,IACjEkB,EAAaQ,GAAGV,KAAKwC,SAzPF,wBAyP6BxD,GAASiL,EAAIjL,IAE7DgB,KAAKwC,SAAS1H,UAAUuP,IA/OG,mBAiP3BnK,EAAaQ,GAAGV,KAAKwC,SAjQD,yBAiQ6BxD,GAAS4K,EAAM5K,IAChEkB,EAAaQ,GAAGV,KAAKwC,SAjQF,wBAiQ6BxD,GAASgL,EAAKhL,IAC9DkB,EAAaQ,GAAGV,KAAKwC,SAjQH,uBAiQ6BxD,GAASiL,EAAIjL,KAIhE0K,SAAS1K,GACH,kBAAkB/E,KAAK+E,EAAMe,OAAOuK,WAzSrB,cA6SftL,EAAMjC,KACRiC,EAAMqD,iBACNrC,KAAKwI,OAAOrB,IA9SM,eA+STnI,EAAMjC,MACfiC,EAAMqD,iBACNrC,KAAKwI,OAAOpB,KAIhBgC,cAAcrS,GAKZ,OAJAiJ,KAAKuH,OAASxQ,GAAWA,EAAQuD,WAC/BkL,EAAeC,KAnQC,iBAmQmB1O,EAAQuD,YAC3C,GAEK0F,KAAKuH,OAAOgD,QAAQxT,GAG7ByT,gBAAgBnB,EAAOoB,GACrB,MAAMC,EAASrB,IAAUpC,EACnB0D,EAAStB,IAAUnC,EACnBiC,EAAcnJ,KAAKoJ,cAAcqB,GACjCG,EAAgB5K,KAAKuH,OAAOxI,OAAS,EAG3C,IAFuB4L,GAA0B,IAAhBxB,GAAuBuB,GAAUvB,IAAgByB,KAE5D5K,KAAK+H,QAAQjB,KACjC,OAAO2D,EAGT,MACMI,GAAa1B,GADLwB,GAAU,EAAI,IACc3K,KAAKuH,OAAOxI,OAEtD,OAAsB,IAAf8L,EACL7K,KAAKuH,OAAOvH,KAAKuH,OAAOxI,OAAS,GACjCiB,KAAKuH,OAAOsD,GAGhBC,mBAAmBC,EAAeC,GAChC,MAAMC,EAAcjL,KAAKoJ,cAAc2B,GACjCG,EAAYlL,KAAKoJ,cAAc5D,EAAeK,QA/R3B,wBA+RyD7F,KAAKwC,WAEvF,OAAOtC,EAAamB,QAAQrB,KAAKwC,SAzThB,oBAyTuC,CACtDuI,cAAAA,EACAtB,UAAWuB,EACXxN,KAAM0N,EACNjC,GAAIgC,IAIRE,2BAA2BpU,GACzB,GAAIiJ,KAAKiI,mBAAoB,CAC3B,MAAMmD,EAAkB5F,EAAeK,QA5SrB,UA4S8C7F,KAAKiI,oBAErEmD,EAAgBtQ,UAAU2C,OAtTN,UAuTpB2N,EAAgB9G,gBAAgB,gBAEhC,MAAM+G,EAAa7F,EAAeC,KA3Sb,mBA2SsCzF,KAAKiI,oBAEhE,IAAK,IAAIpJ,EAAI,EAAGA,EAAIwM,EAAWtM,OAAQF,IACrC,GAAI7G,OAAOsT,SAASD,EAAWxM,GAAG5H,aAAa,oBAAqB,MAAQ+I,KAAKoJ,cAAcrS,GAAU,CACvGsU,EAAWxM,GAAG/D,UAAUuP,IA7TR,UA8ThBgB,EAAWxM,GAAGgF,aAAa,eAAgB,QAC3C,QAMRgF,kBACE,MAAM9R,EAAUiJ,KAAKyH,gBAAkBjC,EAAeK,QA7T7B,wBA6T2D7F,KAAKwC,UAEzF,IAAKzL,EACH,OAGF,MAAMwU,EAAkBvT,OAAOsT,SAASvU,EAAQE,aAAa,oBAAqB,IAE9EsU,GACFvL,KAAK+H,QAAQyD,gBAAkBxL,KAAK+H,QAAQyD,iBAAmBxL,KAAK+H,QAAQrB,SAC5E1G,KAAK+H,QAAQrB,SAAW6E,GAExBvL,KAAK+H,QAAQrB,SAAW1G,KAAK+H,QAAQyD,iBAAmBxL,KAAK+H,QAAQrB,SAIzE8B,OAAOiD,EAAkB1U,GACvB,MAAMsS,EAAQrJ,KAAK0L,kBAAkBD,GAC/BhB,EAAgBjF,EAAeK,QA/UZ,wBA+U0C7F,KAAKwC,UAClEmJ,EAAqB3L,KAAKoJ,cAAcqB,GACxCmB,EAAc7U,GAAWiJ,KAAKwK,gBAAgBnB,EAAOoB,GAErDoB,EAAmB7L,KAAKoJ,cAAcwC,GACtCE,EAAYrL,QAAQT,KAAKwH,WAEzBkD,EAASrB,IAAUpC,EACnB8E,EAAuBrB,EA7VR,sBADF,oBA+VbsB,EAAiBtB,EA7VH,qBACA,qBA6VdM,EAAqBhL,KAAKiM,kBAAkB5C,GAElD,GAAIuC,GAAeA,EAAY9Q,UAAUC,SApWnB,UAqWpBiF,KAAK2H,YAAa,OAKpB,IADmB3H,KAAK8K,mBAAmBc,EAAaZ,GACzCrJ,kBAIV8I,GAAkBmB,EAAvB,CAcA,GATA5L,KAAK2H,YAAa,EAEdmE,GACF9L,KAAK6G,QAGP7G,KAAKmL,2BAA2BS,GAChC5L,KAAKyH,eAAiBmE,EAElB5L,KAAKwC,SAAS1H,UAAUC,SA3XP,SA2XmC,CACtD6Q,EAAY9Q,UAAUuP,IAAI2B,GAE1BvQ,EAAOmQ,GAEPnB,EAAc3P,UAAUuP,IAAI0B,GAC5BH,EAAY9Q,UAAUuP,IAAI0B,GAE1B,MAAMpU,EAAqBD,EAAiC+S,GAE5DvK,EAAaS,IAAI8J,EAAe,gBAAiB,KAC/CmB,EAAY9Q,UAAU2C,OAAOsO,EAAsBC,GACnDJ,EAAY9Q,UAAUuP,IAxYJ,UA0YlBI,EAAc3P,UAAU2C,OA1YN,SA0YgCuO,EAAgBD,GAElE/L,KAAK2H,YAAa,EAElB3O,WAAW,KACTkH,EAAamB,QAAQrB,KAAKwC,SA7ZhB,mBA6ZsC,CAC9CuI,cAAea,EACfnC,UAAWuB,EACXxN,KAAMmO,EACN1C,GAAI4C,KAEL,KAGLpT,EAAqBgS,EAAe9S,QAEpC8S,EAAc3P,UAAU2C,OA1ZJ,UA2ZpBmO,EAAY9Q,UAAUuP,IA3ZF,UA6ZpBrK,KAAK2H,YAAa,EAClBzH,EAAamB,QAAQrB,KAAKwC,SA5aZ,mBA4akC,CAC9CuI,cAAea,EACfnC,UAAWuB,EACXxN,KAAMmO,EACN1C,GAAI4C,IAIJC,GACF9L,KAAK2I,SAIT+C,kBAAkBjC,GAChB,MAAK,CAACrC,EAAiBD,GAAgBhQ,SAASsS,GAI5C3N,IACK2N,IAAcrC,EAAkBF,EAAaD,EAG/CwC,IAAcrC,EAAkBH,EAAaC,EAP3CuC,EAUXwC,kBAAkB5C,GAChB,MAAK,CAACpC,EAAYC,GAAY/P,SAASkS,GAInCvN,IACKuN,IAAUpC,EAAaE,EAAiBC,EAG1CiC,IAAUpC,EAAaG,EAAkBD,EAPvCkC,EAYa1G,yBAAC5L,EAASoC,GAChC,IAAIqK,EAAO3G,EAAKM,IAAIpG,EArfP,eAsfTgR,EAAU,IACTtB,KACAtC,EAAYI,kBAAkBxN,IAGb,iBAAXoC,IACT4O,EAAU,IACLA,KACA5O,IAIP,MAAM+S,EAA2B,iBAAX/S,EAAsBA,EAAS4O,EAAQnB,MAM7D,GAJKpD,IACHA,EAAO,IAAI6D,EAAStQ,EAASgR,IAGT,iBAAX5O,EACTqK,EAAKyF,GAAG9P,QACH,GAAsB,iBAAX+S,EAAqB,CACrC,QAA4B,IAAjB1I,EAAK0I,GACd,MAAM,IAAIhS,UAAW,oBAAmBgS,MAG1C1I,EAAK0I,UACInE,EAAQrB,UAAYqB,EAAQoE,OACrC3I,EAAKqD,QACLrD,EAAKmF,SAIahG,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf8D,EAAS+E,kBAAkBpM,KAAM7G,MAIXwJ,2BAAC3D,GACzB,MAAMe,EAAStI,EAAuBuI,MAEtC,IAAKD,IAAWA,EAAOjF,UAAUC,SAjfT,YAkftB,OAGF,MAAM5B,EAAS,IACVgL,EAAYI,kBAAkBxE,MAC9BoE,EAAYI,kBAAkBvE,OAE7BqM,EAAarM,KAAK/I,aAAa,oBAEjCoV,IACFlT,EAAOuN,UAAW,GAGpBW,EAAS+E,kBAAkBrM,EAAQ5G,GAE/BkT,GACFxP,EAAKM,IAAI4C,EAhjBE,eAgjBgBkJ,GAAGoD,GAGhCrN,EAAMqD,kBAUVnC,EAAaQ,GAAG9J,SAjhBc,6BAkBF,sCA+fyCyQ,EAASiF,qBAE9EpM,EAAaQ,GAAG7I,OAphBa,4BAohBgB,KAC3C,MAAM0U,EAAY/G,EAAeC,KAjgBR,6BAmgBzB,IAAK,IAAI5G,EAAI,EAAGC,EAAMyN,EAAUxN,OAAQF,EAAIC,EAAKD,IAC/CwI,EAAS+E,kBAAkBG,EAAU1N,GAAIhC,EAAKM,IAAIoP,EAAU1N,GAnkB/C,kBA8kBjB7C,EA/kBa,WA+kBYqL,GChlBzB,MAKMZ,EAAU,CACd7C,QAAQ,EACR4I,OAAQ,IAGJxF,EAAc,CAClBpD,OAAQ,UACR4I,OAAQ,oBA0BV,MAAMC,UAAiBnK,EACrBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK0M,kBAAmB,EACxB1M,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK2M,cAAgBnH,EAAeC,KACjC,sCAAiCzF,KAAKwC,SAASoK,qDACJ5M,KAAKwC,SAASoK,QAG5D,MAAMC,EAAarH,EAAeC,KAnBT,+BAqBzB,IAAK,IAAI5G,EAAI,EAAGC,EAAM+N,EAAW9N,OAAQF,EAAIC,EAAKD,IAAK,CACrD,MAAMiO,EAAOD,EAAWhO,GAClB7H,EAAWO,EAAuBuV,GAClCC,EAAgBvH,EAAeC,KAAKzO,GACvC0N,OAAOsI,GAAaA,IAAchN,KAAKwC,UAEzB,OAAbxL,GAAqB+V,EAAchO,SACrCiB,KAAKiN,UAAYjW,EACjBgJ,KAAK2M,cAAcxG,KAAK2G,IAI5B9M,KAAKkN,QAAUlN,KAAK+H,QAAQyE,OAASxM,KAAKmN,aAAe,KAEpDnN,KAAK+H,QAAQyE,QAChBxM,KAAKoN,0BAA0BpN,KAAKwC,SAAUxC,KAAK2M,eAGjD3M,KAAK+H,QAAQnE,QACf5D,KAAK4D,SAMS6C,qBAChB,OAAOA,EAGUhE,sBACjB,MAhFa,cAqFfmB,SACM5D,KAAKwC,SAAS1H,UAAUC,SAlER,QAmElBiF,KAAKqN,OAELrN,KAAKsN,OAITA,OACE,GAAItN,KAAK0M,kBAAoB1M,KAAKwC,SAAS1H,UAAUC,SA1EjC,QA2ElB,OAGF,IAAIwS,EACAC,EAEAxN,KAAKkN,UACPK,EAAU/H,EAAeC,KA1EN,qBA0E6BzF,KAAKkN,SAClDxI,OAAOoI,GAC6B,iBAAxB9M,KAAK+H,QAAQyE,OACfM,EAAK7V,aAAa,oBAAsB+I,KAAK+H,QAAQyE,OAGvDM,EAAKhS,UAAUC,SAvFJ,aA0FC,IAAnBwS,EAAQxO,SACVwO,EAAU,OAId,MAAME,EAAYjI,EAAeK,QAAQ7F,KAAKiN,WAC9C,GAAIM,EAAS,CACX,MAAMG,EAAiBH,EAAQ9H,KAAKqH,GAAQW,IAAcX,GAG1D,GAFAU,EAAcE,EAAiB7Q,EAAKM,IAAIuQ,EAvH7B,eAuHyD,KAEhEF,GAAeA,EAAYd,iBAC7B,OAKJ,GADmBxM,EAAamB,QAAQrB,KAAKwC,SAhH7B,oBAiHDb,iBACb,OAGE4L,GACFA,EAAQhU,QAAQoU,IACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACH3Q,EAAKC,IAAI6Q,EA1IF,cA0IwB,QAKrC,MAAME,EAAY7N,KAAK8N,gBAEvB9N,KAAKwC,SAAS1H,UAAU2C,OA5HA,YA6HxBuC,KAAKwC,SAAS1H,UAAUuP,IA5HE,cA8H1BrK,KAAKwC,SAASnI,MAAMwT,GAAa,EAE7B7N,KAAK2M,cAAc5N,QACrBiB,KAAK2M,cAAcpT,QAAQxC,IACzBA,EAAQ+D,UAAU2C,OAjIG,aAkIrB1G,EAAQ8M,aAAa,iBAAiB,KAI1C7D,KAAK+N,kBAAiB,GAEtB,MAYMC,EAAc,UADSH,EAAU,GAAG1T,cAAgB0T,EAAU1M,MAAM,IAEpExJ,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAff,KACfxC,KAAKwC,SAAS1H,UAAU2C,OA1IA,cA2IxBuC,KAAKwC,SAAS1H,UAAUuP,IA5IF,WADJ,QA+IlBrK,KAAKwC,SAASnI,MAAMwT,GAAa,GAEjC7N,KAAK+N,kBAAiB,GAEtB7N,EAAamB,QAAQrB,KAAKwC,SAxJX,uBAiKjB/J,EAAqBuH,KAAKwC,SAAU7K,GACpCqI,KAAKwC,SAASnI,MAAMwT,GAAgB7N,KAAKwC,SAASwL,GAAhB,KAGpCX,OACE,GAAIrN,KAAK0M,mBAAqB1M,KAAKwC,SAAS1H,UAAUC,SAjKlC,QAkKlB,OAIF,GADmBmF,EAAamB,QAAQrB,KAAKwC,SAzK7B,oBA0KDb,iBACb,OAGF,MAAMkM,EAAY7N,KAAK8N,gBAEvB9N,KAAKwC,SAASnI,MAAMwT,GAAgB7N,KAAKwC,SAASwC,wBAAwB6I,GAAxC,KAElCpS,EAAOuE,KAAKwC,UAEZxC,KAAKwC,SAAS1H,UAAUuP,IA9KE,cA+K1BrK,KAAKwC,SAAS1H,UAAU2C,OAhLA,WADJ,QAmLpB,MAAMwQ,EAAqBjO,KAAK2M,cAAc5N,OAC9C,GAAIkP,EAAqB,EACvB,IAAK,IAAIpP,EAAI,EAAGA,EAAIoP,EAAoBpP,IAAK,CAC3C,MAAMwC,EAAUrB,KAAK2M,cAAc9N,GAC7BiO,EAAOrV,EAAuB4J,GAEhCyL,IAASA,EAAKhS,UAAUC,SAzLZ,UA0LdsG,EAAQvG,UAAUuP,IAvLC,aAwLnBhJ,EAAQwC,aAAa,iBAAiB,IAK5C7D,KAAK+N,kBAAiB,GAStB/N,KAAKwC,SAASnI,MAAMwT,GAAa,GACjC,MAAMlW,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAVf,KACfxC,KAAK+N,kBAAiB,GACtB/N,KAAKwC,SAAS1H,UAAU2C,OAlMA,cAmMxBuC,KAAKwC,SAAS1H,UAAUuP,IApMF,YAqMtBnK,EAAamB,QAAQrB,KAAKwC,SAzMV,wBAgNlB/J,EAAqBuH,KAAKwC,SAAU7K,GAGtCoW,iBAAiBG,GACflO,KAAK0M,iBAAmBwB,EAG1BxL,UACE4E,MAAM5E,UACN1C,KAAK+H,QAAU,KACf/H,KAAKkN,QAAU,KACflN,KAAK2M,cAAgB,KACrB3M,KAAK0M,iBAAmB,KAK1B1E,WAAW7O,GAOT,OANAA,EAAS,IACJsN,KACAtN,IAEEyK,OAASnD,QAAQtH,EAAOyK,QAC/B3K,EAzPS,WAyPaE,EAAQ6N,GACvB7N,EAGT2U,gBACE,OAAO9N,KAAKwC,SAAS1H,UAAUC,SApOrB,SAAA,QACC,SAsOboS,aACE,IAAIX,OAAEA,GAAWxM,KAAK+H,QAElBzP,EAAUkU,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAAShH,EAAeK,QAAQ2G,GAGlC,MAAMxV,EAAY,+CAA0CwV,MAY5D,OAVAhH,EAAeC,KAAKzO,EAAUwV,GAC3BjT,QAAQxC,IACP,MAAMqX,EAAW3W,EAAuBV,GAExCiJ,KAAKoN,0BACHgB,EACA,CAACrX,MAIAyV,EAGTY,0BAA0BrW,EAASsX,GACjC,IAAKtX,IAAYsX,EAAatP,OAC5B,OAGF,MAAMuP,EAASvX,EAAQ+D,UAAUC,SA5Qb,QA8QpBsT,EAAa9U,QAAQuT,IACfwB,EACFxB,EAAKhS,UAAU2C,OA7QM,aA+QrBqP,EAAKhS,UAAUuP,IA/QM,aAkRvByC,EAAKjJ,aAAa,gBAAiByK,KAMf3L,yBAAC5L,EAASoC,GAChC,IAAIqK,EAAO3G,EAAKM,IAAIpG,EAhTP,eAiTb,MAAMgR,EAAU,IACXtB,KACAtC,EAAYI,kBAAkBxN,MACX,iBAAXoC,GAAuBA,EAASA,EAAS,IAWtD,IARKqK,GAAQuE,EAAQnE,QAA4B,iBAAXzK,GAAuB,YAAYc,KAAKd,KAC5E4O,EAAQnE,QAAS,GAGdJ,IACHA,EAAO,IAAIiJ,EAAS1V,EAASgR,IAGT,iBAAX5O,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,MAIawJ,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACfkJ,EAASmB,kBAAkB5N,KAAM7G,OAWvC+G,EAAaQ,GAAG9J,SAnUc,6BAWD,+BAwTyC,SAAUoI,IAEjD,MAAzBA,EAAMe,OAAOuK,SAAoBtL,EAAMiB,gBAAmD,MAAjCjB,EAAMiB,eAAeqK,UAChFtL,EAAMqD,iBAGR,MAAMkM,EAAcpK,EAAYI,kBAAkBvE,MAC5ChJ,EAAWO,EAAuByI,MACfwF,EAAeC,KAAKzO,GAE5BuC,QAAQxC,IACvB,MAAMyM,EAAO3G,EAAKM,IAAIpG,EAhWT,eAiWb,IAAIoC,EACAqK,GAEmB,OAAjBA,EAAK0J,SAAkD,iBAAvBqB,EAAY/B,SAC9ChJ,EAAKuE,QAAQyE,OAAS+B,EAAY/B,OAClChJ,EAAK0J,QAAU1J,EAAK2J,cAGtBhU,EAAS,UAETA,EAASoV,EAGX9B,EAASmB,kBAAkB7W,EAASoC,QAWxC6C,EA1Xa,WA0XYyQ,GCvZlB,IAAIxH,EAAM,MACNuJ,EAAS,SACTC,EAAQ,QACRtJ,EAAO,OAEPuJ,GAAiB,CAACzJ,EAAKuJ,EAAQC,EAAOtJ,GAOtCwJ,GAAmCD,GAAeE,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAInJ,OAAO,CAACoJ,EAAAA,SAAyBA,EAAAA,WAC3C,IACQC,GAA0B,GAAGrJ,OAAOgJ,GAAgB,CAX7C,SAWqDE,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAInJ,OAAO,CAACoJ,EAAWA,EAAAA,SAAyBA,EAAAA,WACtD,IAaQE,GAAiB,CAXJ,aACN,OACK,YAEC,aACN,OACK,YAEE,cACN,QACK,cC7BT,SAASC,GAAYlY,GAClC,OAAOA,GAAWA,EAAQmY,UAAY,IAAInV,cAAgB,KCD7C,SAASoV,GAAUC,GAChC,GAAY,MAARA,EACF,OAAOvX,OAGT,GAAwB,oBAApBuX,EAAKxV,WAAkC,CACzC,IAAIyV,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwBzX,OAG/D,OAAOuX,ECRT,SAAS9W,GAAU8W,GAEjB,OAAOA,aADUD,GAAUC,GAAMzJ,SACIyJ,aAAgBzJ,QAGvD,SAAS4J,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAf7T,aAKJ6T,aADUD,GAAUC,GAAM7T,YACI6T,aAAgB7T,YCyDvD,IAAAmU,GAAe,CACbzT,KAAM,cACN0T,SAAS,EACTC,MAAO,QACPtT,GA5EF,SAAqBuT,GACnB,IAAIC,EAAQD,EAAKC,MACjBzW,OAAOC,KAAKwW,EAAMC,UAAUxW,SAAQ,SAAU0C,GAC5C,IAAI5B,EAAQyV,EAAME,OAAO/T,IAAS,GAC9BuI,EAAasL,EAAMtL,WAAWvI,IAAS,GACvClF,EAAU+Y,EAAMC,SAAS9T,GAExBsT,GAAcxY,IAAakY,GAAYlY,KAO5CsC,OAAO4W,OAAOlZ,EAAQsD,MAAOA,GAC7BhB,OAAOC,KAAKkL,GAAYjL,SAAQ,SAAU0C,GACxC,IAAIvC,EAAQ8K,EAAWvI,IAET,IAAVvC,EACF3C,EAAQuN,gBAAgBrI,GAExBlF,EAAQ8M,aAAa5H,GAAgB,IAAVvC,EAAiB,GAAKA,WAwDvDwW,OAlDF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MACdM,EAAgB,CAClBC,OAAQ,CACNhL,SAAUyK,EAAMQ,QAAQC,SACxBpL,KAAM,IACNF,IAAK,IACLuL,OAAQ,KAEVC,MAAO,CACLpL,SAAU,YAEZqL,UAAW,IASb,OAPArX,OAAO4W,OAAOH,EAAMC,SAASM,OAAOhW,MAAO+V,EAAcC,QACzDP,EAAME,OAASI,EAEXN,EAAMC,SAASU,OACjBpX,OAAO4W,OAAOH,EAAMC,SAASU,MAAMpW,MAAO+V,EAAcK,OAGnD,WACLpX,OAAOC,KAAKwW,EAAMC,UAAUxW,SAAQ,SAAU0C,GAC5C,IAAIlF,EAAU+Y,EAAMC,SAAS9T,GACzBuI,EAAasL,EAAMtL,WAAWvI,IAAS,GAGvC5B,EAFkBhB,OAAOC,KAAKwW,EAAME,OAAOW,eAAe1U,GAAQ6T,EAAME,OAAO/T,GAAQmU,EAAcnU,IAE7E2S,QAAO,SAAUvU,EAAOb,GAElD,OADAa,EAAMb,GAAY,GACXa,IACN,IAEEkV,GAAcxY,IAAakY,GAAYlY,KAI5CsC,OAAO4W,OAAOlZ,EAAQsD,MAAOA,GAC7BhB,OAAOC,KAAKkL,GAAYjL,SAAQ,SAAUqX,GACxC7Z,EAAQuN,gBAAgBsM,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBhC,GACvC,OAAOA,EAAUzX,MAAM,KAAK,GCFf,SAAS2N,GAAsBjO,GAC5C,IAAIgO,EAAOhO,EAAQiO,wBACnB,MAAO,CACL+L,MAAOhM,EAAKgM,MACZC,OAAQjM,EAAKiM,OACb/L,IAAKF,EAAKE,IACVwJ,MAAO1J,EAAK0J,MACZD,OAAQzJ,EAAKyJ,OACbrJ,KAAMJ,EAAKI,KACX8L,EAAGlM,EAAKI,KACR+L,EAAGnM,EAAKE,KCPG,SAASkM,GAAcpa,GACpC,IAAIqa,EAAapM,GAAsBjO,GAGnCga,EAAQha,EAAQsa,YAChBL,EAASja,EAAQ2E,aAUrB,OARIjF,KAAK+S,IAAI4H,EAAWL,MAAQA,IAAU,IACxCA,EAAQK,EAAWL,OAGjBta,KAAK+S,IAAI4H,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAGla,EAAQwO,WACX2L,EAAGna,EAAQuO,UACXyL,MAAOA,EACPC,OAAQA,GCrBG,SAASjW,GAASyR,EAAQzG,GACvC,IAAIuL,EAAWvL,EAAM1K,aAAe0K,EAAM1K,cAE1C,GAAImR,EAAOzR,SAASgL,GAClB,OAAO,EAEJ,GAAIuL,GAAY7B,GAAa6B,GAAW,CACzC,IAAI/K,EAAOR,EAEX,EAAG,CACD,GAAIQ,GAAQiG,EAAO+E,WAAWhL,GAC5B,OAAO,EAITA,EAAOA,EAAKjM,YAAciM,EAAKiL,WACxBjL,GAIb,OAAO,ECpBM,SAASzO,GAAiBf,GACvC,OAAOoY,GAAUpY,GAASe,iBAAiBf,GCD9B,SAAS0a,GAAe1a,GACrC,MAAO,CAAC,QAAS,KAAM,MAAMwT,QAAQ0E,GAAYlY,KAAa,ECDjD,SAAS2a,GAAmB3a,GAEzC,QAASuB,GAAUvB,GAAWA,EAAQsY,cACtCtY,EAAQH,WAAaiB,OAAOjB,UAAUuE,gBCDzB,SAASwW,GAAc5a,GACpC,MAA6B,SAAzBkY,GAAYlY,GACPA,EAMPA,EAAQ6a,cACR7a,EAAQuD,aACRmV,GAAa1Y,GAAWA,EAAQya,KAAO,OAEvCE,GAAmB3a,GCRvB,SAAS8a,GAAoB9a,GAC3B,OAAKwY,GAAcxY,IACoB,UAAvCe,GAAiBf,GAASsO,SAInBtO,EAAQ+a,aAHN,KA6BI,SAASC,GAAgBhb,GAItC,IAHA,IAAIc,EAASsX,GAAUpY,GACnB+a,EAAeD,GAAoB9a,GAEhC+a,GAAgBL,GAAeK,IAA6D,WAA5Cha,GAAiBga,GAAczM,UACpFyM,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B7C,GAAY6C,IAA0D,SAA9B7C,GAAY6C,IAAwE,WAA5Cha,GAAiBga,GAAczM,UAC3HxN,EAGFia,GAjCT,SAA4B/a,GAI1B,IAHA,IAAIib,GAAsE,IAA1D7J,UAAU8J,UAAUlY,cAAcwQ,QAAQ,WACtD2H,EAAcP,GAAc5a,GAEzBwY,GAAc2C,IAAgB,CAAC,OAAQ,QAAQ3H,QAAQ0E,GAAYiD,IAAgB,GAAG,CAC3F,IAAIC,EAAMra,GAAiBoa,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAe/H,QAAQ4H,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAIzN,QAAyB,SAAfyN,EAAIzN,OACjO,OAAOwN,EAEPA,EAAcA,EAAY5X,WAI9B,OAAO,KAiBgBkY,CAAmBzb,IAAYc,ECnDzC,SAAS4a,GAAyB3D,GAC/C,MAAO,CAAC,MAAO,UAAUvE,QAAQuE,IAAc,EAAI,IAAM,ICDpD,IAAI4D,GAAMjc,KAAKic,IACXC,GAAMlc,KAAKkc,IACXC,GAAQnc,KAAKmc,MCDT,SAASC,GAAOF,EAAKjZ,EAAOgZ,GACzC,OAAOI,GAAQH,EAAKI,GAAQrZ,EAAOgZ,ICDtB,SAASM,GAAmBC,GACzC,OAAO5Z,OAAO4W,OAAO,GCDd,CACLhL,IAAK,EACLwJ,MAAO,EACPD,OAAQ,EACRrJ,KAAM,GDHuC8N,GEFlC,SAASC,GAAgBxZ,EAAOJ,GAC7C,OAAOA,EAAKsV,QAAO,SAAUuE,EAASpW,GAEpC,OADAoW,EAAQpW,GAAOrD,EACRyZ,IACN,ICwFL,IAAAC,GAAe,CACbnX,KAAM,QACN0T,SAAS,EACTC,MAAO,OACPtT,GA9EF,SAAeuT,GACb,IAAIwD,EAEAvD,EAAQD,EAAKC,MACb7T,EAAO4T,EAAK5T,KACZqU,EAAUT,EAAKS,QACfgD,EAAexD,EAAMC,SAASU,MAC9B8C,EAAgBzD,EAAM0D,cAAcD,cACpCE,EAAgB3C,GAAiBhB,EAAMhB,WACvC4E,EAAOjB,GAAyBgB,GAEhC3U,EADa,CAACqG,EAAMsJ,GAAOlE,QAAQkJ,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBU,EAAS7D,GAItD,OAAOkD,GAAsC,iBAH7CW,EAA6B,mBAAZA,EAAyBA,EAAQta,OAAO4W,OAAO,GAAIH,EAAM8D,MAAO,CAC/E9E,UAAWgB,EAAMhB,aACb6E,GACkDA,EAAUT,GAAgBS,EAASjF,KAoBvEmF,CAAgBvD,EAAQqD,QAAS7D,GACjDgE,EAAY3C,GAAcmC,GAC1BS,EAAmB,MAATL,EAAezO,EAAME,EAC/B6O,EAAmB,MAATN,EAAelF,EAASC,EAClCwF,EAAUnE,EAAM8D,MAAMlD,UAAU5R,GAAOgR,EAAM8D,MAAMlD,UAAUgD,GAAQH,EAAcG,GAAQ5D,EAAM8D,MAAMvD,OAAOvR,GAC9GoV,EAAYX,EAAcG,GAAQ5D,EAAM8D,MAAMlD,UAAUgD,GACxDS,EAAoBpC,GAAgBuB,GACpCc,EAAaD,EAA6B,MAATT,EAAeS,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9CvB,EAAMM,EAAcc,GACpBrB,EAAM0B,EAAaN,EAAUhV,GAAOmU,EAAce,GAClDQ,EAASJ,EAAa,EAAIN,EAAUhV,GAAO,EAAIyV,EAC/CzP,EAAS+N,GAAOF,EAAK6B,EAAQ9B,GAE7B+B,EAAWf,EACf5D,EAAM0D,cAAcvX,KAASoX,EAAwB,IAA0BoB,GAAY3P,EAAQuO,EAAsBqB,aAAe5P,EAAS0P,EAAQnB,KA6CzJnD,OA1CF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MAEd6E,EADUxE,EAAMG,QACWvZ,QAC3Buc,OAAoC,IAArBqB,EAA8B,sBAAwBA,EAErD,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAexD,EAAMC,SAASM,OAAO7Y,cAAc8b,MAahDvY,GAAS+U,EAAMC,SAASM,OAAQiD,KAQrCxD,EAAMC,SAASU,MAAQ6C,IAUvBzC,SAAU,CAAC,iBACX+D,iBAAkB,CAAC,oBC3FjBC,GAAa,CACf5P,IAAK,OACLwJ,MAAO,OACPD,OAAQ,OACRrJ,KAAM,QAgBD,SAAS2P,GAAY3E,GAC1B,IAAI4E,EAEA1E,EAASF,EAAME,OACf2E,EAAa7E,EAAM6E,WACnBlG,EAAYqB,EAAMrB,UAClBmG,EAAU9E,EAAM8E,QAChB5P,EAAW8K,EAAM9K,SACjB6P,EAAkB/E,EAAM+E,gBACxBC,EAAWhF,EAAMgF,SACjBC,EAAejF,EAAMiF,aAErBC,GAAyB,IAAjBD,EAvBd,SAA2BvF,GACzB,IAAIoB,EAAIpB,EAAKoB,EACTC,EAAIrB,EAAKqB,EAEToE,EADMzd,OACI0d,kBAAoB,EAClC,MAAO,CACLtE,EAAG2B,GAAMA,GAAM3B,EAAIqE,GAAOA,IAAQ,EAClCpE,EAAG0B,GAAMA,GAAM1B,EAAIoE,GAAOA,IAAQ,GAgBAE,CAAkBP,GAAmC,mBAAjBG,EAA8BA,EAAaH,GAAWA,EAC1HQ,EAAUJ,EAAMpE,EAChBA,OAAgB,IAAZwE,EAAqB,EAAIA,EAC7BC,EAAUL,EAAMnE,EAChBA,OAAgB,IAAZwE,EAAqB,EAAIA,EAE7BC,EAAOV,EAAQtE,eAAe,KAC9BiF,EAAOX,EAAQtE,eAAe,KAC9BkF,EAAQ1Q,EACR2Q,EAAQ7Q,EACR8Q,EAAMle,OAEV,GAAIsd,EAAU,CACZ,IAAIrD,EAAeC,GAAgB1B,GAC/B2F,EAAa,eACbC,EAAY,cAEZnE,IAAiB3C,GAAUkB,IAGmB,WAA5CvY,GAFJga,EAAeJ,GAAmBrB,IAEChL,WACjC2Q,EAAa,eACbC,EAAY,eAKhBnE,EAAeA,EAEXhD,IAAc7J,IAChB6Q,EAAQtH,EAER0C,GAAKY,EAAakE,GAAchB,EAAWhE,OAC3CE,GAAKgE,EAAkB,GAAK,GAG1BpG,IAAc3J,IAChB0Q,EAAQpH,EAERwC,GAAKa,EAAamE,GAAajB,EAAWjE,MAC1CE,GAAKiE,EAAkB,GAAK,GAIhC,IAKMgB,EALFC,EAAe9c,OAAO4W,OAAO,CAC/B5K,SAAUA,GACT8P,GAAYN,IAEf,OAAIK,EAGK7b,OAAO4W,OAAO,GAAIkG,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe9D,WAAa2D,EAAIR,kBAAoB,GAAK,EAAI,aAAetE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAUgF,IAG3R7c,OAAO4W,OAAO,GAAIkG,IAAepB,EAAkB,IAAoBe,GAASF,EAAO1E,EAAI,KAAO,GAAI6D,EAAgBc,GAASF,EAAO1E,EAAI,KAAO,GAAI8D,EAAgB3C,UAAY,GAAI2C,IAsD9L,IAAAqB,GAAe,CACbna,KAAM,gBACN0T,SAAS,EACTC,MAAO,cACPtT,GAvDF,SAAuB+Z,GACrB,IAAIvG,EAAQuG,EAAMvG,MACdQ,EAAU+F,EAAM/F,QAChBgG,EAAwBhG,EAAQ4E,gBAChCA,OAA4C,IAA1BoB,GAA0CA,EAC5DC,EAAoBjG,EAAQ6E,SAC5BA,OAAiC,IAAtBoB,GAAsCA,EACjDC,EAAwBlG,EAAQ8E,aAChCA,OAAyC,IAA1BoB,GAA0CA,EAYzDL,EAAe,CACjBrH,UAAWgC,GAAiBhB,EAAMhB,WAClCuB,OAAQP,EAAMC,SAASM,OACvB2E,WAAYlF,EAAM8D,MAAMvD,OACxB6E,gBAAiBA,GAGsB,MAArCpF,EAAM0D,cAAcD,gBACtBzD,EAAME,OAAOK,OAAShX,OAAO4W,OAAO,GAAIH,EAAME,OAAOK,OAAQyE,GAAYzb,OAAO4W,OAAO,GAAIkG,EAAc,CACvGlB,QAASnF,EAAM0D,cAAcD,cAC7BlO,SAAUyK,EAAMQ,QAAQC,SACxB4E,SAAUA,EACVC,aAAcA,OAIe,MAA7BtF,EAAM0D,cAAc/C,QACtBX,EAAME,OAAOS,MAAQpX,OAAO4W,OAAO,GAAIH,EAAME,OAAOS,MAAOqE,GAAYzb,OAAO4W,OAAO,GAAIkG,EAAc,CACrGlB,QAASnF,EAAM0D,cAAc/C,MAC7BpL,SAAU,WACV8P,UAAU,EACVC,aAAcA,OAIlBtF,EAAMtL,WAAW6L,OAAShX,OAAO4W,OAAO,GAAIH,EAAMtL,WAAW6L,OAAQ,CACnEoG,wBAAyB3G,EAAMhB,aAUjCtL,KAAM,ICvJJkT,GAAU,CACZA,SAAS,GAsCXC,GAAe,CACb1a,KAAM,iBACN0T,SAAS,EACTC,MAAO,QACPtT,GAAI,aACJ4T,OAxCF,SAAgBL,GACd,IAAIC,EAAQD,EAAKC,MACb9S,EAAW6S,EAAK7S,SAChBsT,EAAUT,EAAKS,QACfsG,EAAkBtG,EAAQuG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkBxG,EAAQyG,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Cjf,EAASsX,GAAUW,EAAMC,SAASM,QAClC2G,EAAgB,GAAGtR,OAAOoK,EAAMkH,cAActG,UAAWZ,EAAMkH,cAAc3G,QAYjF,OAVIwG,GACFG,EAAczd,SAAQ,SAAU0d,GAC9BA,EAAape,iBAAiB,SAAUmE,EAASka,OAAQR,OAIzDK,GACFlf,EAAOgB,iBAAiB,SAAUmE,EAASka,OAAQR,IAG9C,WACDG,GACFG,EAAczd,SAAQ,SAAU0d,GAC9BA,EAAale,oBAAoB,SAAUiE,EAASka,OAAQR,OAI5DK,GACFlf,EAAOkB,oBAAoB,SAAUiE,EAASka,OAAQR,MAY1DlT,KAAM,IC/CJ2T,GAAO,CACThS,KAAM,QACNsJ,MAAO,OACPD,OAAQ,MACRvJ,IAAK,UAEQ,SAASmS,GAAqBtI,GAC3C,OAAOA,EAAUvP,QAAQ,0BAA0B,SAAU8X,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACTvN,MAAO,MACPK,IAAK,SAEQ,SAASqN,GAA8BxI,GACpD,OAAOA,EAAUvP,QAAQ,cAAc,SAAU8X,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgBnI,GACtC,IAAI2G,EAAM5G,GAAUC,GAGpB,MAAO,CACLhK,WAHe2Q,EAAIyB,YAInBtS,UAHc6Q,EAAI0B,aCDP,SAASC,GAAoB3gB,GAQ1C,OAAOiO,GAAsB0M,GAAmB3a,IAAUoO,KAAOoS,GAAgBxgB,GAASqO,WCV7E,SAASuS,GAAe5gB,GAErC,IAAI6gB,EAAoB9f,GAAiBf,GACrC8gB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6B9d,KAAK4d,EAAWE,EAAYD,GCGnD,SAASE,GAAkBjhB,EAASkhB,GACjD,IAAIC,OAES,IAATD,IACFA,EAAO,IAGT,IAAIhB,ECdS,SAASkB,EAAgB/I,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAa7E,QAAQ0E,GAAYG,KAAU,EAEvDA,EAAKC,cAAcxT,KAGxB0T,GAAcH,IAASuI,GAAevI,GACjCA,EAGF+I,EAAgBxG,GAAcvC,IDIlB+I,CAAgBphB,GAC/BqhB,EAASnB,KAAqE,OAAlDiB,EAAwBnhB,EAAQsY,oBAAyB,EAAS6I,EAAsBrc,MACpHka,EAAM5G,GAAU8H,GAChBlX,EAASqY,EAAS,CAACrC,GAAKrQ,OAAOqQ,EAAIsC,gBAAkB,GAAIV,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GqB,EAAcL,EAAKvS,OAAO3F,GAC9B,OAAOqY,EAASE,EAChBA,EAAY5S,OAAOsS,GAAkBrG,GAAc5R,KExBtC,SAASwY,GAAiBxT,GACvC,OAAO1L,OAAO4W,OAAO,GAAIlL,EAAM,CAC7BI,KAAMJ,EAAKkM,EACXhM,IAAKF,EAAKmM,EACVzC,MAAO1J,EAAKkM,EAAIlM,EAAKgM,MACrBvC,OAAQzJ,EAAKmM,EAAInM,EAAKiM,SCuB1B,SAASwH,GAA2BzhB,EAAS0hB,GAC3C,M/BpBoB,a+BoBbA,EAA8BF,GC1BxB,SAAyBxhB,GACtC,IAAIgf,EAAM5G,GAAUpY,GAChB2hB,EAAOhH,GAAmB3a,GAC1BshB,EAAiBtC,EAAIsC,eACrBtH,EAAQ2H,EAAKpE,YACbtD,EAAS0H,EAAKrE,aACdpD,EAAI,EACJC,EAAI,EAuBR,OAjBImH,IACFtH,EAAQsH,EAAetH,MACvBC,EAASqH,EAAerH,OASnB,iCAAiC/W,KAAKkO,UAAU8J,aACnDhB,EAAIoH,EAAe9S,WACnB2L,EAAImH,EAAe/S,YAIhB,CACLyL,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EAAIyG,GAAoB3gB,GAC3Bma,EAAGA,GDRiDyH,CAAgB5hB,IAAYwY,GAAckJ,GAdlG,SAAoC1hB,GAClC,IAAIgO,EAAOC,GAAsBjO,GASjC,OARAgO,EAAKE,IAAMF,EAAKE,IAAMlO,EAAQ6hB,UAC9B7T,EAAKI,KAAOJ,EAAKI,KAAOpO,EAAQ8hB,WAChC9T,EAAKyJ,OAASzJ,EAAKE,IAAMlO,EAAQsd,aACjCtP,EAAK0J,MAAQ1J,EAAKI,KAAOpO,EAAQud,YACjCvP,EAAKgM,MAAQha,EAAQud,YACrBvP,EAAKiM,OAASja,EAAQsd,aACtBtP,EAAKkM,EAAIlM,EAAKI,KACdJ,EAAKmM,EAAInM,EAAKE,IACPF,EAI2G+T,CAA2BL,GAAkBF,GEtBlJ,SAAyBxhB,GACtC,IAAImhB,EAEAQ,EAAOhH,GAAmB3a,GAC1BgiB,EAAYxB,GAAgBxgB,GAC5B8E,EAA0D,OAAlDqc,EAAwBnhB,EAAQsY,oBAAyB,EAAS6I,EAAsBrc,KAChGkV,EAAQ2B,GAAIgG,EAAKM,YAAaN,EAAKpE,YAAazY,EAAOA,EAAKmd,YAAc,EAAGnd,EAAOA,EAAKyY,YAAc,GACvGtD,EAAS0B,GAAIgG,EAAKO,aAAcP,EAAKrE,aAAcxY,EAAOA,EAAKod,aAAe,EAAGpd,EAAOA,EAAKwY,aAAe,GAC5GpD,GAAK8H,EAAU3T,WAAasS,GAAoB3gB,GAChDma,GAAK6H,EAAU7T,UAMnB,MAJiD,QAA7CpN,GAAiB+D,GAAQ6c,GAAMjP,YACjCwH,GAAKyB,GAAIgG,EAAKpE,YAAazY,EAAOA,EAAKyY,YAAc,GAAKvD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFG2KgI,CAAgBxH,GAAmB3a,KG7BtM,SAASoiB,GAAarK,GACnC,OAAOA,EAAUzX,MAAM,KAAK,GCGf,SAAS+hB,GAAevJ,GACrC,IAOIoF,EAPAvE,EAAYb,EAAKa,UACjB3Z,EAAU8Y,EAAK9Y,QACf+X,EAAYe,EAAKf,UACjB2E,EAAgB3E,EAAYgC,GAAiBhC,GAAa,KAC1DuK,EAAYvK,EAAYqK,GAAarK,GAAa,KAClDwK,EAAU5I,EAAUO,EAAIP,EAAUK,MAAQ,EAAIha,EAAQga,MAAQ,EAC9DwI,EAAU7I,EAAUQ,EAAIR,EAAUM,OAAS,EAAIja,EAAQia,OAAS,EAGpE,OAAQyC,GACN,KAAKxO,EACHgQ,EAAU,CACRhE,EAAGqI,EACHpI,EAAGR,EAAUQ,EAAIna,EAAQia,QAE3B,MAEF,KAAKxC,EACHyG,EAAU,CACRhE,EAAGqI,EACHpI,EAAGR,EAAUQ,EAAIR,EAAUM,QAE7B,MAEF,KAAKvC,EACHwG,EAAU,CACRhE,EAAGP,EAAUO,EAAIP,EAAUK,MAC3BG,EAAGqI,GAEL,MAEF,KAAKpU,EACH8P,EAAU,CACRhE,EAAGP,EAAUO,EAAIla,EAAQga,MACzBG,EAAGqI,GAEL,MAEF,QACEtE,EAAU,CACRhE,EAAGP,EAAUO,EACbC,EAAGR,EAAUQ,GAInB,IAAIsI,EAAW/F,EAAgBhB,GAAyBgB,GAAiB,KAEzE,GAAgB,MAAZ+F,EAAkB,CACpB,IAAI1a,EAAmB,MAAb0a,EAAmB,SAAW,QAExC,OAAQH,GACN,InClDa,QmCmDXpE,EAAQuE,GAAYvE,EAAQuE,IAAa9I,EAAU5R,GAAO,EAAI/H,EAAQ+H,GAAO,GAC7E,MAEF,InCrDW,MmCsDTmW,EAAQuE,GAAYvE,EAAQuE,IAAa9I,EAAU5R,GAAO,EAAI/H,EAAQ+H,GAAO,IAOnF,OAAOmW,EC1DM,SAASwE,GAAe3J,EAAOQ,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXqJ,EAAqBD,EAAS5K,UAC9BA,OAAmC,IAAvB6K,EAAgC7J,EAAMhB,UAAY6K,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EpCXY,kBoCWqCA,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EpCZC,WoCY6CA,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EpCbH,SoCa+CA,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAAS/F,QAC5BA,OAA+B,IAArByG,EAA8B,EAAIA,EAC5CnH,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAASjF,KACpG2L,EpCnBc,WoCmBDJ,EpClBI,YADH,SoCoBdK,EAAmBxK,EAAMC,SAASW,UAClCsE,EAAalF,EAAM8D,MAAMvD,OACzBtZ,EAAU+Y,EAAMC,SAASoK,EAAcE,EAAaJ,GACpDM,ELmBS,SAAyBxjB,EAAS8iB,EAAUE,GACzD,IAAIS,EAAmC,oBAAbX,EAlB5B,SAA4B9iB,GAC1B,IAAI0jB,EAAkBzC,GAAkBrG,GAAc5a,IAElD2jB,EADoB,CAAC,WAAY,SAASnQ,QAAQzS,GAAiBf,GAASsO,WAAa,GACnDkK,GAAcxY,GAAWgb,GAAgBhb,GAAWA,EAE9F,OAAKuB,GAAUoiB,GAKRD,EAAgB/V,QAAO,SAAU+T,GACtC,OAAOngB,GAAUmgB,IAAmB1d,GAAS0d,EAAgBiC,IAAmD,SAAhCzL,GAAYwJ,MALrF,GAYkDkC,CAAmB5jB,GAAW,GAAG2O,OAAOmU,GAC/FY,EAAkB,GAAG/U,OAAO8U,EAAqB,CAACT,IAClDa,EAAsBH,EAAgB,GACtCI,EAAeJ,EAAgB7L,QAAO,SAAUkM,EAASrC,GAC3D,IAAI1T,EAAOyT,GAA2BzhB,EAAS0hB,GAK/C,OAJAqC,EAAQ7V,IAAMyN,GAAI3N,EAAKE,IAAK6V,EAAQ7V,KACpC6V,EAAQrM,MAAQkE,GAAI5N,EAAK0J,MAAOqM,EAAQrM,OACxCqM,EAAQtM,OAASmE,GAAI5N,EAAKyJ,OAAQsM,EAAQtM,QAC1CsM,EAAQ3V,KAAOuN,GAAI3N,EAAKI,KAAM2V,EAAQ3V,MAC/B2V,IACNtC,GAA2BzhB,EAAS6jB,IAKvC,OAJAC,EAAa9J,MAAQ8J,EAAapM,MAAQoM,EAAa1V,KACvD0V,EAAa7J,OAAS6J,EAAarM,OAASqM,EAAa5V,IACzD4V,EAAa5J,EAAI4J,EAAa1V,KAC9B0V,EAAa3J,EAAI2J,EAAa5V,IACvB4V,EKnCkBE,CAAgBziB,GAAUvB,GAAWA,EAAUA,EAAQikB,gBAAkBtJ,GAAmB5B,EAAMC,SAASM,QAASwJ,EAAUE,GACnJkB,EAAsBjW,GAAsBsV,GAC5C/G,EAAgB6F,GAAe,CACjC1I,UAAWuK,EACXlkB,QAASie,EACTzE,SAAU,WACVzB,UAAWA,IAEToM,EAAmB3C,GAAiBlf,OAAO4W,OAAO,GAAI+E,EAAYzB,IAClE4H,EpChCc,WoCgCMlB,EAA4BiB,EAAmBD,EAGnEG,EAAkB,CACpBnW,IAAKsV,EAAmBtV,IAAMkW,EAAkBlW,IAAMgO,EAAchO,IACpEuJ,OAAQ2M,EAAkB3M,OAAS+L,EAAmB/L,OAASyE,EAAczE,OAC7ErJ,KAAMoV,EAAmBpV,KAAOgW,EAAkBhW,KAAO8N,EAAc9N,KACvEsJ,MAAO0M,EAAkB1M,MAAQ8L,EAAmB9L,MAAQwE,EAAcxE,OAExE4M,EAAavL,EAAM0D,cAAc1O,OAErC,GpC3CkB,WoC2CdmV,GAA6BoB,EAAY,CAC3C,IAAIvW,EAASuW,EAAWvM,GACxBzV,OAAOC,KAAK8hB,GAAiB7hB,SAAQ,SAAUwD,GAC7C,IAAIue,EAAW,CAAC7M,EAAOD,GAAQjE,QAAQxN,IAAQ,EAAI,GAAK,EACpD2W,EAAO,CAACzO,EAAKuJ,GAAQjE,QAAQxN,IAAQ,EAAI,IAAM,IACnDqe,EAAgBre,IAAQ+H,EAAO4O,GAAQ4H,KAI3C,OAAOF,EC1DM,SAASG,GAAqBzL,EAAOQ,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXxB,EAAY4K,EAAS5K,UACrB+K,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBpG,EAAU+F,EAAS/F,QACnB6H,EAAiB9B,EAAS8B,eAC1BC,EAAwB/B,EAASgC,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EpC,EAAYF,GAAarK,GACzBC,EAAasK,EAAYmC,EAAiB7M,GAAsBA,GAAoBjK,QAAO,SAAUoK,GACvG,OAAOqK,GAAarK,KAAeuK,KAChC3K,GACDkN,EAAoB7M,EAAWrK,QAAO,SAAUoK,GAClD,OAAO4M,EAAsBnR,QAAQuE,IAAc,KAGpB,IAA7B8M,EAAkB7c,SACpB6c,EAAoB7M,GAQtB,IAAI8M,EAAYD,EAAkBhN,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa2K,GAAe3J,EAAO,CACrChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdpG,QAASA,IACR7C,GAAiBhC,IACbD,IACN,IACH,OAAOxV,OAAOC,KAAKuiB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,IAAAC,GAAe,CACbhgB,KAAM,OACN0T,SAAS,EACTC,MAAO,OACPtT,GA5HF,SAAcuT,GACZ,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfrU,EAAO4T,EAAK5T,KAEhB,IAAI6T,EAAM0D,cAAcvX,GAAMigB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB7L,EAAQkJ,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB/L,EAAQgM,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BlM,EAAQmM,mBACtC9I,EAAUrD,EAAQqD,QAClBkG,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBuC,EAAwBpM,EAAQkL,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBpL,EAAQoL,sBAChCiB,EAAqB7M,EAAMQ,QAAQxB,UACnC2E,EAAgB3C,GAAiB6L,GAEjCF,EAAqBD,IADH/I,IAAkBkJ,GACqCnB,EAjC/E,SAAuC1M,GACrC,GtCLgB,SsCKZgC,GAAiBhC,GACnB,MAAO,GAGT,IAAI8N,EAAoBxF,GAAqBtI,GAC7C,MAAO,CAACwI,GAA8BxI,GAAY8N,EAAmBtF,GAA8BsF,IA2BwCC,CAA8BF,GAA3E,CAACvF,GAAqBuF,KAChH5N,EAAa,CAAC4N,GAAoBjX,OAAO+W,GAAoB7N,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAInJ,OtCvCG,SsCuCIoL,GAAiBhC,GAAsByM,GAAqBzL,EAAO,CACnFhB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdpG,QAASA,EACT6H,eAAgBA,EAChBE,sBAAuBA,IACpB5M,KACJ,IACCgO,EAAgBhN,EAAM8D,MAAMlD,UAC5BsE,EAAalF,EAAM8D,MAAMvD,OACzB0M,EAAY,IAAIngB,IAChBogB,GAAqB,EACrBC,EAAwBlO,EAAW,GAE9BlQ,EAAI,EAAGA,EAAIkQ,EAAWhQ,OAAQF,IAAK,CAC1C,IAAIiQ,EAAYC,EAAWlQ,GAEvBqe,EAAiBpM,GAAiBhC,GAElCqO,EtCzDW,UsCyDQhE,GAAarK,GAChCsO,EAAa,CAACnY,EAAKuJ,GAAQjE,QAAQ2S,IAAmB,EACtDpe,EAAMse,EAAa,QAAU,SAC7BvF,EAAW4B,GAAe3J,EAAO,CACnChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACbxG,QAASA,IAEP0J,EAAoBD,EAAaD,EAAmB1O,EAAQtJ,EAAOgY,EAAmB3O,EAASvJ,EAE/F6X,EAAche,GAAOkW,EAAWlW,KAClCue,EAAoBjG,GAAqBiG,IAG3C,IAAIC,EAAmBlG,GAAqBiG,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAOpX,KAAK0R,EAASqF,IAAmB,GAGtCX,GACFgB,EAAOpX,KAAK0R,EAASwF,IAAsB,EAAGxF,EAASyF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBnO,EACxBkO,GAAqB,EACrB,MAGFD,EAAUjgB,IAAIgS,EAAWyO,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmB7O,EAAWtJ,MAAK,SAAUqJ,GAC/C,IAAIyO,EAASR,EAAU5f,IAAI2R,GAE3B,GAAIyO,EACF,OAAOA,EAAOpc,MAAM,EAAGwc,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYnC,EAAiB,EAAI,EAmBZmC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpC7N,EAAMhB,YAAcmO,IACtBnN,EAAM0D,cAAcvX,GAAMigB,OAAQ,EAClCpM,EAAMhB,UAAYmO,EAClBnN,EAAM+N,OAAQ,KAUhBjJ,iBAAkB,CAAC,UACnBpR,KAAM,CACJ0Y,OAAO,IC7IX,SAAS4B,GAAejG,EAAU9S,EAAMgZ,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB9M,EAAG,EACHC,EAAG,IAIA,CACLjM,IAAK4S,EAAS5S,IAAMF,EAAKiM,OAAS+M,EAAiB7M,EACnDzC,MAAOoJ,EAASpJ,MAAQ1J,EAAKgM,MAAQgN,EAAiB9M,EACtDzC,OAAQqJ,EAASrJ,OAASzJ,EAAKiM,OAAS+M,EAAiB7M,EACzD/L,KAAM0S,EAAS1S,KAAOJ,EAAKgM,MAAQgN,EAAiB9M,GAIxD,SAAS+M,GAAsBnG,GAC7B,MAAO,CAAC5S,EAAKwJ,EAAOD,EAAQrJ,GAAM8Y,MAAK,SAAUC,GAC/C,OAAOrG,EAASqG,IAAS,KAiC7B,IAAAC,GAAe,CACbliB,KAAM,OACN0T,SAAS,EACTC,MAAO,OACPgF,iBAAkB,CAAC,mBACnBtY,GAlCF,SAAcuT,GACZ,IAAIC,EAAQD,EAAKC,MACb7T,EAAO4T,EAAK5T,KACZ6gB,EAAgBhN,EAAM8D,MAAMlD,UAC5BsE,EAAalF,EAAM8D,MAAMvD,OACzB0N,EAAmBjO,EAAM0D,cAAc4K,gBACvCC,EAAoB5E,GAAe3J,EAAO,CAC5CmK,eAAgB,cAEdqE,EAAoB7E,GAAe3J,EAAO,CAC5CqK,aAAa,IAEXoE,EAA2BT,GAAeO,EAAmBvB,GAC7D0B,EAAsBV,GAAeQ,EAAmBtJ,EAAY+I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7C1O,EAAM0D,cAAcvX,GAAQ,CAC1BsiB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpB5O,EAAMtL,WAAW6L,OAAShX,OAAO4W,OAAO,GAAIH,EAAMtL,WAAW6L,OAAQ,CACnEsO,+BAAgCF,EAChCG,sBAAuBF,MCH3BG,GAAe,CACb5iB,KAAM,SACN0T,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXvU,GA5BF,SAAgB6T,GACd,IAAIL,EAAQK,EAAML,MACdQ,EAAUH,EAAMG,QAChBrU,EAAOkU,EAAMlU,KACb6iB,EAAkBxO,EAAQxL,OAC1BA,OAA6B,IAApBga,EAA6B,CAAC,EAAG,GAAKA,EAC/Ctb,EAAOuL,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW8E,EAAO9O,GACxD,IAAI2O,EAAgB3C,GAAiBhC,GACjCiQ,EAAiB,CAAC5Z,EAAMF,GAAKsF,QAAQkJ,IAAkB,GAAK,EAAI,EAEhE5D,EAAyB,mBAAX/K,EAAwBA,EAAOzL,OAAO4W,OAAO,GAAI2D,EAAO,CACxE9E,UAAWA,KACPhK,EACFka,EAAWnP,EAAK,GAChBoP,EAAWpP,EAAK,GAIpB,OAFAmP,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAAC5Z,EAAMsJ,GAAOlE,QAAQkJ,IAAkB,EAAI,CACjDxC,EAAGgO,EACH/N,EAAG8N,GACD,CACF/N,EAAG+N,EACH9N,EAAG+N,GAWcC,CAAwBpQ,EAAWgB,EAAM8D,MAAO9O,GAC1D+J,IACN,IACCsQ,EAAwB3b,EAAKsM,EAAMhB,WACnCmC,EAAIkO,EAAsBlO,EAC1BC,EAAIiO,EAAsBjO,EAEW,MAArCpB,EAAM0D,cAAcD,gBACtBzD,EAAM0D,cAAcD,cAActC,GAAKA,EACvCnB,EAAM0D,cAAcD,cAAcrC,GAAKA,GAGzCpB,EAAM0D,cAAcvX,GAAQuH,ICxB9B4b,GAAe,CACbnjB,KAAM,gBACN0T,SAAS,EACTC,MAAO,OACPtT,GApBF,SAAuBuT,GACrB,IAAIC,EAAQD,EAAKC,MACb7T,EAAO4T,EAAK5T,KAKhB6T,EAAM0D,cAAcvX,GAAQmd,GAAe,CACzC1I,UAAWZ,EAAM8D,MAAMlD,UACvB3Z,QAAS+Y,EAAM8D,MAAMvD,OACrBE,SAAU,WACVzB,UAAWgB,EAAMhB,aAUnBtL,KAAM,IC6FR6b,GAAe,CACbpjB,KAAM,kBACN0T,SAAS,EACTC,MAAO,OACPtT,GA5GF,SAAyBuT,GACvB,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfrU,EAAO4T,EAAK5T,KACZkgB,EAAoB7L,EAAQkJ,SAC5B4C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB/L,EAAQgM,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDxC,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBxG,EAAUrD,EAAQqD,QAClB2L,EAAkBhP,EAAQiP,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBlP,EAAQmP,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtD3H,EAAW4B,GAAe3J,EAAO,CACnC+J,SAAUA,EACVE,aAAcA,EACdpG,QAASA,EACTwG,YAAaA,IAEX1G,EAAgB3C,GAAiBhB,EAAMhB,WACvCuK,EAAYF,GAAarJ,EAAMhB,WAC/B4Q,GAAmBrG,EACnBG,EAAW/G,GAAyBgB,GACpC6I,ECrCY,MDqCS9C,ECrCH,IAAM,IDsCxBjG,EAAgBzD,EAAM0D,cAAcD,cACpCuJ,EAAgBhN,EAAM8D,MAAMlD,UAC5BsE,EAAalF,EAAM8D,MAAMvD,OACzBsP,EAA4C,mBAAjBF,EAA8BA,EAAapmB,OAAO4W,OAAO,GAAIH,EAAM8D,MAAO,CACvG9E,UAAWgB,EAAMhB,aACb2Q,EACFjc,EAAO,CACTyN,EAAG,EACHC,EAAG,GAGL,GAAKqC,EAAL,CAIA,GAAI6I,GAAiBG,EAAc,CACjC,IAAIqD,EAAwB,MAAbpG,EAAmBvU,EAAME,EACpC0a,EAAuB,MAAbrG,EAAmBhL,EAASC,EACtC3P,EAAmB,MAAb0a,EAAmB,SAAW,QACpC1U,EAASyO,EAAciG,GACvB7G,EAAMY,EAAciG,GAAY3B,EAAS+H,GACzClN,EAAMa,EAAciG,GAAY3B,EAASgI,GACzCC,EAAWP,GAAUvK,EAAWlW,GAAO,EAAI,EAC3CihB,E1CxDW,U0CwDF1G,EAAsByD,EAAche,GAAOkW,EAAWlW,GAC/DkhB,E1CzDW,U0CyDF3G,GAAuBrE,EAAWlW,IAAQge,EAAche,GAGjEwU,EAAexD,EAAMC,SAASU,MAC9BqD,EAAYyL,GAAUjM,EAAenC,GAAcmC,GAAgB,CACrEvC,MAAO,EACPC,OAAQ,GAENiP,EAAqBnQ,EAAM0D,cAAc,oBAAsB1D,EAAM0D,cAAc,oBAAoBG,QxBtEtG,CACL1O,IAAK,EACLwJ,MAAO,EACPD,OAAQ,EACRrJ,KAAM,GwBmEF+a,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWvN,GAAO,EAAGiK,EAAche,GAAMgV,EAAUhV,IACnDuhB,EAAYX,EAAkB5C,EAAche,GAAO,EAAIghB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB5C,EAAche,GAAO,EAAIghB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JxL,EAAoBrE,EAAMC,SAASU,OAASsB,GAAgBjC,EAAMC,SAASU,OAC3E8P,EAAepM,EAAiC,MAAbqF,EAAmBrF,EAAkByE,WAAa,EAAIzE,EAAkB0E,YAAc,EAAI,EAC7H2H,EAAsB1Q,EAAM0D,cAAc1O,OAASgL,EAAM0D,cAAc1O,OAAOgL,EAAMhB,WAAW0K,GAAY,EAC3GiH,EAAYlN,EAAciG,GAAY6G,EAAYG,EAAsBD,EACxEG,EAAYnN,EAAciG,GAAY8G,EAAYE,EAEtD,GAAIpE,EAAe,CACjB,IAAIuE,EAAkB9N,GAAO0M,EAASxM,GAAQJ,EAAK8N,GAAa9N,EAAK7N,EAAQya,EAASzM,GAAQJ,EAAKgO,GAAahO,GAChHa,EAAciG,GAAYmH,EAC1Bnd,EAAKgW,GAAYmH,EAAkB7b,EAGrC,GAAIyX,EAAc,CAChB,IAAIqE,GAAyB,MAAbpH,EAAmBvU,EAAME,EAErC0b,GAAwB,MAAbrH,EAAmBhL,EAASC,EAEvCqS,GAAUvN,EAAc+I,GAExByE,GAAOD,GAAUjJ,EAAS+I,IAE1BI,GAAOF,GAAUjJ,EAASgJ,IAE1BI,GAAmBpO,GAAO0M,EAASxM,GAAQgO,GAAMN,GAAaM,GAAMD,GAASvB,EAASzM,GAAQkO,GAAMN,GAAaM,IAErHzN,EAAc+I,GAAW2E,GACzBzd,EAAK8Y,GAAW2E,GAAmBH,IAIvChR,EAAM0D,cAAcvX,GAAQuH,IAS5BoR,iBAAkB,CAAC,WEhHN,SAASsM,GAAiBC,EAAyBrP,EAAcsP,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICVoChS,ECJOrY,EFcvCoE,EAAkBuW,GAAmBI,GACrC/M,EAAOC,GAAsBmc,GAC7BE,EAA0B9R,GAAcuC,GACxC+E,EAAS,CACXzR,WAAY,EACZF,UAAW,GAET+P,EAAU,CACZhE,EAAG,EACHC,EAAG,GAkBL,OAfImQ,IAA4BA,IAA4BD,MACxB,SAA9BnS,GAAY6C,IAChB6F,GAAexc,MACb0b,GCzBgCzH,EDyBT0C,KCxBd3C,GAAUC,IAAUG,GAAcH,GCJxC,CACLhK,YAFyCrO,EDQbqY,GCNRhK,WACpBF,UAAWnO,EAAQmO,WDGZqS,GAAgBnI,ID0BnBG,GAAcuC,KAChBmD,EAAUjQ,GAAsB8M,IACxBb,GAAKa,EAAa+G,WAC1B5D,EAAQ/D,GAAKY,EAAa8G,WACjBzd,IACT8Z,EAAQhE,EAAIyG,GAAoBvc,KAI7B,CACL8V,EAAGlM,EAAKI,KAAO0R,EAAOzR,WAAa6P,EAAQhE,EAC3CC,EAAGnM,EAAKE,IAAM4R,EAAO3R,UAAY+P,EAAQ/D,EACzCH,MAAOhM,EAAKgM,MACZC,OAAQjM,EAAKiM,QG7BjB,IAAIsQ,GAAkB,CACpBxS,UAAW,SACXyS,UAAW,GACXhR,SAAU,YAGZ,SAASiR,KACP,IAAK,IAAIC,EAAOC,UAAU3iB,OAAQuC,EAAO,IAAI/D,MAAMkkB,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ErgB,EAAKqgB,GAAQD,UAAUC,GAGzB,OAAQrgB,EAAK2c,MAAK,SAAUlnB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQiO,0BAIhC,SAAS4c,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCX,GAAkBW,EAC3E,OAAO,SAAsBvR,EAAWL,EAAQC,QAC9B,IAAZA,IACFA,EAAU4R,GAGZ,IC/C6B5lB,EAC3B6lB,ED8CErS,EAAQ,CACVhB,UAAW,SACXsT,iBAAkB,GAClB9R,QAASjX,OAAO4W,OAAO,GAAIqR,GAAiBY,GAC5C1O,cAAe,GACfzD,SAAU,CACRW,UAAWA,EACXL,OAAQA,GAEV7L,WAAY,GACZwL,OAAQ,IAENqS,EAAmB,GACnBC,GAAc,EACdtlB,EAAW,CACb8S,MAAOA,EACPyS,WAAY,SAAoBjS,GAC9BkS,IACA1S,EAAMQ,QAAUjX,OAAO4W,OAAO,GAAIiS,EAAgBpS,EAAMQ,QAASA,GACjER,EAAMkH,cAAgB,CACpBtG,UAAWpY,GAAUoY,GAAasH,GAAkBtH,GAAaA,EAAUsK,eAAiBhD,GAAkBtH,EAAUsK,gBAAkB,GAC1I3K,OAAQ2H,GAAkB3H,IAI5B,IExE4BkR,EAC9BkB,EFuEML,EGtCG,SAAwBb,GAErC,IAAIa,EAlCN,SAAeb,GACb,IAAImB,EAAM,IAAI9lB,IACV+lB,EAAU,IAAIvkB,IACdwkB,EAAS,GA0Bb,OAzBArB,EAAUhoB,SAAQ,SAAUspB,GAC1BH,EAAI5lB,IAAI+lB,EAAS5mB,KAAM4mB,MAkBzBtB,EAAUhoB,SAAQ,SAAUspB,GACrBF,EAAQ1lB,IAAI4lB,EAAS5mB,OAhB5B,SAAS6f,EAAK+G,GACZF,EAAQtY,IAAIwY,EAAS5mB,MACN,GAAGyJ,OAAOmd,EAAShS,UAAY,GAAIgS,EAASjO,kBAAoB,IACtErb,SAAQ,SAAUupB,GACzB,IAAKH,EAAQ1lB,IAAI6lB,GAAM,CACrB,IAAIC,EAAcL,EAAIvlB,IAAI2lB,GAEtBC,GACFjH,EAAKiH,OAIXH,EAAOzc,KAAK0c,GAMV/G,CAAK+G,MAGFD,EAKgBvZ,CAAMkY,GAE7B,OAAOvS,GAAeJ,QAAO,SAAUC,EAAKe,GAC1C,OAAOf,EAAInJ,OAAO0c,EAAiB1d,QAAO,SAAUme,GAClD,OAAOA,EAASjT,QAAUA,QAE3B,IH8B0BoT,EExEKzB,EFwEsB,GAAG7b,OAAOsc,EAAkBlS,EAAMQ,QAAQiR,WEvE9FkB,EAASlB,EAAU3S,QAAO,SAAU6T,EAAQQ,GAC9C,IAAIC,EAAWT,EAAOQ,EAAQhnB,MAK9B,OAJAwmB,EAAOQ,EAAQhnB,MAAQinB,EAAW7pB,OAAO4W,OAAO,GAAIiT,EAAUD,EAAS,CACrE3S,QAASjX,OAAO4W,OAAO,GAAIiT,EAAS5S,QAAS2S,EAAQ3S,SACrD9M,KAAMnK,OAAO4W,OAAO,GAAIiT,EAAS1f,KAAMyf,EAAQzf,QAC5Cyf,EACER,IACN,IAEIppB,OAAOC,KAAKmpB,GAAQC,KAAI,SAAU3lB,GACvC,OAAO0lB,EAAO1lB,QFsGV,OAvCA+S,EAAMsS,iBAAmBA,EAAiB1d,QAAO,SAAUye,GACzD,OAAOA,EAAExT,WAqJbG,EAAMsS,iBAAiB7oB,SAAQ,SAAU8b,GACvC,IAAIpZ,EAAOoZ,EAAMpZ,KACbmnB,EAAgB/N,EAAM/E,QACtBA,OAA4B,IAAlB8S,EAA2B,GAAKA,EAC1ClT,EAASmF,EAAMnF,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAImT,EAAYnT,EAAO,CACrBJ,MAAOA,EACP7T,KAAMA,EACNe,SAAUA,EACVsT,QAASA,IAKX+R,EAAiBlc,KAAKkd,GAFT,kBA7HRrmB,EAASka,UAOlBoM,YAAa,WACX,IAAIhB,EAAJ,CAIA,IAAIiB,EAAkBzT,EAAMC,SACxBW,EAAY6S,EAAgB7S,UAC5BL,EAASkT,EAAgBlT,OAG7B,GAAKmR,GAAiB9Q,EAAWL,GAAjC,CASAP,EAAM8D,MAAQ,CACZlD,UAAWwQ,GAAiBxQ,EAAWqB,GAAgB1B,GAAoC,UAA3BP,EAAMQ,QAAQC,UAC9EF,OAAQc,GAAcd,IAOxBP,EAAM+N,OAAQ,EACd/N,EAAMhB,UAAYgB,EAAMQ,QAAQxB,UAKhCgB,EAAMsS,iBAAiB7oB,SAAQ,SAAUspB,GACvC,OAAO/S,EAAM0D,cAAcqP,EAAS5mB,MAAQ5C,OAAO4W,OAAO,GAAI4S,EAASrf,SAIzE,IAAK,IAAI0F,EAAQ,EAAGA,EAAQ4G,EAAMsS,iBAAiBrjB,OAAQmK,IAUzD,IAAoB,IAAhB4G,EAAM+N,MAAV,CAMA,IAAI2F,EAAwB1T,EAAMsS,iBAAiBlZ,GAC/C5M,EAAKknB,EAAsBlnB,GAC3BmnB,EAAyBD,EAAsBlT,QAC/CoJ,OAAsC,IAA3B+J,EAAoC,GAAKA,EACpDxnB,EAAOunB,EAAsBvnB,KAEf,mBAAPK,IACTwT,EAAQxT,EAAG,CACTwT,MAAOA,EACPQ,QAASoJ,EACTzd,KAAMA,EACNe,SAAUA,KACN8S,QAjBNA,EAAM+N,OAAQ,EACd3U,GAAS,KAsBfgO,QCjM2B5a,EDiMV,WACf,OAAO,IAAIonB,SAAQ,SAAUC,GAC3B3mB,EAASsmB,cACTK,EAAQ7T,OClMT,WAUL,OATKqS,IACHA,EAAU,IAAIuB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBzB,OAAU0B,EACVF,EAAQrnB,YAKP6lB,ID2LL2B,QAAS,WACPtB,IACAF,GAAc,IAIlB,IAAKd,GAAiB9Q,EAAWL,GAK/B,OAAOrT,EAmCT,SAASwlB,IACPH,EAAiB9oB,SAAQ,SAAU+C,GACjC,OAAOA,OAET+lB,EAAmB,GAGrB,OAvCArlB,EAASulB,WAAWjS,GAASsT,MAAK,SAAU9T,IACrCwS,GAAehS,EAAQyT,eAC1BzT,EAAQyT,cAAcjU,MAqCnB9S,GAGJ,IAAIgnB,GAA4BpC,KIzPnCoC,GAA4BpC,GAAgB,CAC9CI,iBAFqB,CAACrL,GAAgBpD,GAAe0Q,GAAeC,MCMlEF,GAA4BpC,GAAgB,CAC9CI,iBAFqB,CAACrL,GAAgBpD,GAAe0Q,GAAeC,GAAapf,GAAQqf,GAAM/F,GAAiB3N,GAAOpD,uKpDNvG,+BAEC,YACF,sBACY,2BACP,kBACF,mBACG,4DAQC,kBACN,iBACK,uBAEC,kBACN,iBACK,wBAEE,oBACN,mBACK,0JqDCxB,MAYM+W,GAAiB,IAAIpqB,OAAQ,4BAuB7BqqB,GAAgBvoB,IAAU,UAAY,YACtCwoB,GAAmBxoB,IAAU,YAAc,UAC3CyoB,GAAmBzoB,IAAU,aAAe,eAC5C0oB,GAAsB1oB,IAAU,eAAiB,aACjD2oB,GAAkB3oB,IAAU,aAAe,cAC3C4oB,GAAiB5oB,IAAU,cAAgB,aAE3C2K,GAAU,CACd3B,OAAQ,CAAC,EAAG,GACZ+U,SAAU,kBACVnJ,UAAW,SACXjW,QAAS,UACTkqB,aAAc,MAGV3d,GAAc,CAClBlC,OAAQ,0BACR+U,SAAU,mBACVnJ,UAAW,0BACXjW,QAAS,SACTkqB,aAAc,0BAShB,MAAMC,WAAiBtiB,EACrBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK6kB,QAAU,KACf7kB,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK8kB,MAAQ9kB,KAAK+kB,kBAClB/kB,KAAKglB,UAAYhlB,KAAKilB,gBAEtBjlB,KAAKuI,qBAKW9B,qBAChB,OAAOA,GAGaO,yBACpB,OAAOA,GAGUvE,sBACjB,MAtFa,cA2FfmB,SACE,GAAI5D,KAAKwC,SAASxH,UAAYgF,KAAKwC,SAAS1H,UAAUC,SAtE9B,YAuEtB,OAGF,MAAMmqB,EAAWllB,KAAKwC,SAAS1H,UAAUC,SAzErB,QA2EpB6pB,GAASO,aAELD,GAIJllB,KAAKsN,OAGPA,OACE,GAAItN,KAAKwC,SAASxH,UAAYgF,KAAKwC,SAAS1H,UAAUC,SAtF9B,aAsF+DiF,KAAK8kB,MAAMhqB,UAAUC,SArFxF,QAsFlB,OAGF,MAAMyR,EAASoY,GAASQ,qBAAqBplB,KAAKwC,UAC5CuI,EAAgB,CACpBA,cAAe/K,KAAKwC,UAKtB,IAFkBtC,EAAamB,QAAQrB,KAAKwC,SAtG5B,mBAsGkDuI,GAEpDpJ,iBAAd,CAKA,GAAI3B,KAAKglB,UACP7gB,EAAYC,iBAAiBpE,KAAK8kB,MAAO,SAAU,YAC9C,CACL,QAAsB,IAAXO,GACT,MAAM,IAAInrB,UAAU,gEAGtB,IAAIogB,EAAmBta,KAAKwC,SAEG,WAA3BxC,KAAK+H,QAAQ2I,UACf4J,EAAmB9N,EACVlU,EAAU0H,KAAK+H,QAAQ2I,YAChC4J,EAAmBta,KAAK+H,QAAQ2I,eAGa,IAAlC1Q,KAAK+H,QAAQ2I,UAAUvC,SAChCmM,EAAmBta,KAAK+H,QAAQ2I,UAAU,KAED,iBAA3B1Q,KAAK+H,QAAQ2I,YAC7B4J,EAAmBta,KAAK+H,QAAQ2I,WAGlC,MAAMiU,EAAe3kB,KAAKslB,mBACpBC,EAAkBZ,EAAapD,UAAU9b,KAAKod,GAA8B,gBAAlBA,EAAS5mB,OAA+C,IAArB4mB,EAASlT,SAE5G3P,KAAK6kB,QAAUQ,GAAoB/K,EAAkBta,KAAK8kB,MAAOH,GAE7DY,GACFphB,EAAYC,iBAAiBpE,KAAK8kB,MAAO,SAAU,UAQnD,iBAAkBluB,SAASuE,kBAC5BqR,EAAOpJ,QAlIc,gBAmItB,GAAGsC,UAAU9O,SAASiF,KAAKiK,UACxBvM,QAAQuT,GAAQ5M,EAAaQ,GAAGoM,EAAM,YAAa,M/DAzC,gB+DGf9M,KAAKwC,SAASgjB,QACdxlB,KAAKwC,SAASqB,aAAa,iBAAiB,GAE5C7D,KAAK8kB,MAAMhqB,UAAU8I,OAlJD,QAmJpB5D,KAAKwC,SAAS1H,UAAU8I,OAnJJ,QAoJpB1D,EAAamB,QAAQrB,KAAKwC,SA3JT,oBA2JgCuI,IAGnDsC,OACE,GAAIrN,KAAKwC,SAASxH,UAAYgF,KAAKwC,SAAS1H,UAAUC,SAzJ9B,cAyJgEiF,KAAK8kB,MAAMhqB,UAAUC,SAxJzF,QAyJlB,OAGF,MAAMgQ,EAAgB,CACpBA,cAAe/K,KAAKwC,UAGJtC,EAAamB,QAAQrB,KAAKwC,SA1K5B,mBA0KkDuI,GAEpDpJ,mBAIV3B,KAAK6kB,SACP7kB,KAAK6kB,QAAQf,UAGf9jB,KAAK8kB,MAAMhqB,UAAU8I,OA1KD,QA2KpB5D,KAAKwC,SAAS1H,UAAU8I,OA3KJ,QA4KpBO,EAAYE,oBAAoBrE,KAAK8kB,MAAO,UAC5C5kB,EAAamB,QAAQrB,KAAKwC,SAtLR,qBAsLgCuI,IAGpDrI,UACExC,EAAaC,IAAIH,KAAKwC,SAvMP,gBAwMfxC,KAAK8kB,MAAQ,KAET9kB,KAAK6kB,UACP7kB,KAAK6kB,QAAQf,UACb9jB,KAAK6kB,QAAU,MAGjBvd,MAAM5E,UAGRwU,SACElX,KAAKglB,UAAYhlB,KAAKilB,gBAClBjlB,KAAK6kB,SACP7kB,KAAK6kB,QAAQ3N,SAMjB3O,qBACErI,EAAaQ,GAAGV,KAAKwC,SA5MJ,oBA4M2BxD,IAC1CA,EAAMqD,iBACNrC,KAAK4D,WAIToE,WAAW7O,GAST,GARAA,EAAS,IACJ6G,KAAKuC,YAAYkE,WACjBtC,EAAYI,kBAAkBvE,KAAKwC,aACnCrJ,GAGLF,EA3OS,WA2OaE,EAAQ6G,KAAKuC,YAAYyE,aAEf,iBAArB7N,EAAOuX,YAA2BpY,EAAUa,EAAOuX,YACV,mBAA3CvX,EAAOuX,UAAU1L,sBAGxB,MAAM,IAAI9K,UAjPH,WAiPqBC,cAAP,kGAGvB,OAAOhB,EAGT4rB,kBACE,OAAOvf,EAAee,KAAKvG,KAAKwC,SAzNd,kBAyNuC,GAG3DijB,gBACE,MAAMC,EAAiB1lB,KAAKwC,SAASlI,WAErC,GAAIorB,EAAe5qB,UAAUC,SApON,WAqOrB,OAAO0pB,GAGT,GAAIiB,EAAe5qB,UAAUC,SAvOJ,aAwOvB,OAAO2pB,GAIT,MAAMiB,EAAkF,QAA1E7tB,iBAAiBkI,KAAK8kB,OAAOc,iBAAiB,iBAAiBtuB,OAE7E,OAAIouB,EAAe5qB,UAAUC,SAhPP,UAiPb4qB,EAAQrB,GAAmBD,GAG7BsB,EAAQnB,GAAsBD,GAGvCU,gBACE,OAA0D,OAAnDjlB,KAAKwC,SAASY,QAAS,WAGhCyiB,aACE,MAAM/gB,OAAEA,GAAW9E,KAAK+H,QAExB,MAAsB,iBAAXjD,EACFA,EAAOzN,MAAM,KAAKqrB,IAAI3e,GAAO/L,OAAOsT,SAASvH,EAAK,KAGrC,mBAAXe,EACFghB,GAAchhB,EAAOghB,EAAY9lB,KAAKwC,UAGxCsC,EAGTwgB,mBACE,MAAMS,EAAwB,CAC5BjX,UAAW9O,KAAKylB,gBAChBlE,UAAW,CAAC,CACVtlB,KAAM,kBACNqU,QAAS,CACPuJ,SAAU7Z,KAAK+H,QAAQ8R,WAG3B,CACE5d,KAAM,SACNqU,QAAS,CACPxL,OAAQ9E,KAAK6lB,iBAanB,MAP6B,WAAzB7lB,KAAK+H,QAAQtN,UACfsrB,EAAsBxE,UAAY,CAAC,CACjCtlB,KAAM,cACN0T,SAAS,KAIN,IACFoW,KACsC,mBAA9B/lB,KAAK+H,QAAQ4c,aAA8B3kB,KAAK+H,QAAQ4c,aAAaoB,GAAyB/lB,KAAK+H,QAAQ4c,cAMlGhiB,yBAAC5L,EAASoC,GAChC,IAAIqK,EAAO3G,EAAKM,IAAIpG,EAnUP,eA0Ub,GAJKyM,IACHA,EAAO,IAAIohB,GAAS7tB,EAHY,iBAAXoC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,MAIawJ,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACfqhB,GAASoB,kBAAkBhmB,KAAM7G,MAIpBwJ,kBAAC3D,GAChB,GAAIA,EAAO,CACT,GAlVqB,IAkVjBA,EAAMkF,QAAiD,UAAflF,EAAMoB,MArVxC,QAqV4DpB,EAAMjC,IAC1E,OAGF,GAAI,8BAA8B9C,KAAK+E,EAAMe,OAAOuK,SAClD,OAIJ,MAAM2b,EAAUzgB,EAAeC,KAvUN,+BAyUzB,IAAK,IAAI5G,EAAI,EAAGC,EAAMmnB,EAAQlnB,OAAQF,EAAIC,EAAKD,IAAK,CAClD,MAAMqnB,EAAUrpB,EAAKM,IAAI8oB,EAAQpnB,GAvWtB,eAwWLkM,EAAgB,CACpBA,cAAekb,EAAQpnB,IAOzB,GAJIG,GAAwB,UAAfA,EAAMoB,OACjB2K,EAAcob,WAAannB,IAGxBknB,EACH,SAGF,MAAME,EAAeF,EAAQpB,MAC7B,GAAKmB,EAAQpnB,GAAG/D,UAAUC,SA9VR,QA8VlB,CAIA,GAAIiE,EAAO,CAET,GAAI,CAACknB,EAAQ1jB,UAAUyb,KAAKlnB,GAAWiI,EAAMqnB,eAAelvB,SAASJ,IACnE,SAIF,GAAmB,UAAfiI,EAAMoB,MA1XF,QA0XsBpB,EAAMjC,KAAmBqpB,EAAarrB,SAASiE,EAAMe,QACjF,SAIcG,EAAamB,QAAQ4kB,EAAQpnB,GAxXjC,mBAwXiDkM,GACjDpJ,mBAMV,iBAAkB/K,SAASuE,iBAC7B,GAAGuK,UAAU9O,SAASiF,KAAKiK,UACxBvM,QAAQuT,GAAQ5M,EAAaC,IAAI2M,EAAM,YAAa,M/D3O5C,gB+D8ObmZ,EAAQpnB,GAAGgF,aAAa,gBAAiB,SAErCqiB,EAAQrB,SACVqB,EAAQrB,QAAQf,UAGlBsC,EAAatrB,UAAU2C,OAhYL,QAiYlBwoB,EAAQpnB,GAAG/D,UAAU2C,OAjYH,QAkYlB0G,EAAYE,oBAAoB+hB,EAAc,UAC9ClmB,EAAamB,QAAQ4kB,EAAQpnB,GA5Yb,qBA4Y+BkM,MAIxBpI,4BAAC5L,GAC1B,OAAOU,EAAuBV,IAAYA,EAAQuD,WAGxBqI,6BAAC3D,GAQ3B,GAAI,kBAAkB/E,KAAK+E,EAAMe,OAAOuK,SAra1B,UAsaZtL,EAAMjC,KAvaO,WAuaeiC,EAAMjC,MAnajB,cAoafiC,EAAMjC,KAraO,YAqamBiC,EAAMjC,KACtCiC,EAAMe,OAAOqD,QA/YC,oBAgZfghB,GAAenqB,KAAK+E,EAAMjC,KAC3B,OAMF,GAHAiC,EAAMqD,iBACNrD,EAAMsnB,kBAEFtmB,KAAKhF,UAAYgF,KAAKlF,UAAUC,SA/ZZ,YAgatB,OAGF,MAAMyR,EAASoY,GAASQ,qBAAqBplB,MACvCklB,EAAWllB,KAAKlF,UAAUC,SAnaZ,QAqapB,GAxbe,WAwbXiE,EAAMjC,IAIR,OAHeiD,KAAKgG,QAhaG,+BAga6BhG,KAAOwF,EAAeY,KAAKpG,KAhaxD,+BAgaoF,IACpGwlB,aACPZ,GAASO,aAIX,IAAKD,IA5bY,YA4bClmB,EAAMjC,KA3bL,cA2b6BiC,EAAMjC,KAGpD,YAFeiD,KAAKgG,QAvaG,+BAua6BhG,KAAOwF,EAAeY,KAAKpG,KAvaxD,+BAuaoF,IACpGumB,QAIT,IAAKrB,GApcS,UAocGlmB,EAAMjC,IAErB,YADA6nB,GAASO,aAIX,MAAMqB,EAAQhhB,EAAeC,KA9aF,8DA8a+B+G,GAAQ9H,OAAOtK,GAEzE,IAAKosB,EAAMznB,OACT,OAGF,IAAImK,EAAQsd,EAAMjc,QAAQvL,EAAMe,QA7cf,YAgdbf,EAAMjC,KAAwBmM,EAAQ,GACxCA,IAhdiB,cAodflK,EAAMjC,KAA0BmM,EAAQsd,EAAMznB,OAAS,GACzDmK,IAIFA,GAAmB,IAAXA,EAAe,EAAIA,EAE3Bsd,EAAMtd,GAAOsc,SAUjBtlB,EAAaQ,GAAG9J,SA1dgB,+BAUH,8BAgd2CguB,GAAS6B,uBACjFvmB,EAAaQ,GAAG9J,SA3dgB,+BAWV,iBAgd2CguB,GAAS6B,uBAC1EvmB,EAAaQ,GAAG9J,SA7dc,6BA6dkBguB,GAASO,YACzDjlB,EAAaQ,GAAG9J,SA5dc,6BA4dkBguB,GAASO,YACzDjlB,EAAaQ,GAAG9J,SA/dc,6BAWD,+BAodyC,SAAUoI,GAC9EA,EAAMqD,iBACNuiB,GAASoB,kBAAkBhmB,SAU7BhE,EA9fa,WA8fY4oB,IC/fzB,MAMMne,GAAU,CACdigB,UAAU,EACV/f,UAAU,EACV6e,OAAO,GAGHxe,GAAc,CAClB0f,SAAU,mBACV/f,SAAU,UACV6e,MAAO,WAoCT,MAAMmB,WAAcrkB,EAClBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK4mB,QAAUphB,EAAeK,QAlBV,gBAkBmC7F,KAAKwC,UAC5DxC,KAAK6mB,UAAY,KACjB7mB,KAAK8mB,UAAW,EAChB9mB,KAAK+mB,oBAAqB,EAC1B/mB,KAAKgnB,sBAAuB,EAC5BhnB,KAAK0M,kBAAmB,EACxB1M,KAAKinB,gBAAkB,EAKPxgB,qBAChB,OAAOA,GAGUhE,sBACjB,MAvEa,WA4EfmB,OAAOmH,GACL,OAAO/K,KAAK8mB,SAAW9mB,KAAKqN,OAASrN,KAAKsN,KAAKvC,GAGjDuC,KAAKvC,GACH,GAAI/K,KAAK8mB,UAAY9mB,KAAK0M,iBACxB,OAGE1M,KAAKknB,gBACPlnB,KAAK0M,kBAAmB,GAG1B,MAAMya,EAAYjnB,EAAamB,QAAQrB,KAAKwC,SArE5B,gBAqEkD,CAChEuI,cAAAA,IAGE/K,KAAK8mB,UAAYK,EAAUxlB,mBAI/B3B,KAAK8mB,UAAW,EAEhB9mB,KAAKonB,kBACLpnB,KAAKqnB,gBAELrnB,KAAKsnB,gBAELtnB,KAAKunB,kBACLvnB,KAAKwnB,kBAELtnB,EAAaQ,GAAGV,KAAKwC,SAnFI,yBAgBC,4BAmEiDxD,GAASgB,KAAKqN,KAAKrO,IAE9FkB,EAAaQ,GAAGV,KAAK4mB,QAlFQ,6BAkF0B,KACrD1mB,EAAaS,IAAIX,KAAKwC,SApFG,2BAoF8BxD,IACjDA,EAAMe,SAAWC,KAAKwC,WACxBxC,KAAKgnB,sBAAuB,OAKlChnB,KAAKynB,cAAc,IAAMznB,KAAK0nB,aAAa3c,KAG7CsC,KAAKrO,GAKH,GAJIA,GACFA,EAAMqD,kBAGHrC,KAAK8mB,UAAY9mB,KAAK0M,iBACzB,OAKF,GAFkBxM,EAAamB,QAAQrB,KAAKwC,SAhH5B,iBAkHFb,iBACZ,OAGF3B,KAAK8mB,UAAW,EAChB,MAAMa,EAAa3nB,KAAKknB,cAgBxB,GAdIS,IACF3nB,KAAK0M,kBAAmB,GAG1B1M,KAAKunB,kBACLvnB,KAAKwnB,kBAELtnB,EAAaC,IAAIvJ,SA3HE,oBA6HnBoJ,KAAKwC,SAAS1H,UAAU2C,OAjHJ,QAmHpByC,EAAaC,IAAIH,KAAKwC,SA7HG,0BA8HzBtC,EAAaC,IAAIH,KAAK4mB,QA3HO,8BA6HzBe,EAAY,CACd,MAAMhwB,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiBxD,GAASgB,KAAK4nB,WAAW5oB,IAC1EvG,EAAqBuH,KAAKwC,SAAU7K,QAEpCqI,KAAK4nB,aAITllB,UACE,CAAC7K,OAAQmI,KAAKwC,SAAUxC,KAAK4mB,SAC1BrtB,QAAQsuB,GAAe3nB,EAAaC,IAAI0nB,EAnK5B,cAqKfvgB,MAAM5E,UAONxC,EAAaC,IAAIvJ,SAvJE,oBAyJnBoJ,KAAK+H,QAAU,KACf/H,KAAK4mB,QAAU,KACf5mB,KAAK6mB,UAAY,KACjB7mB,KAAK8mB,SAAW,KAChB9mB,KAAK+mB,mBAAqB,KAC1B/mB,KAAKgnB,qBAAuB,KAC5BhnB,KAAK0M,iBAAmB,KACxB1M,KAAKinB,gBAAkB,KAGzBa,eACE9nB,KAAKsnB,gBAKPtf,WAAW7O,GAMT,OALAA,EAAS,IACJsN,MACAtN,GAELF,EArMS,QAqMaE,EAAQ6N,IACvB7N,EAGTuuB,aAAa3c,GACX,MAAM4c,EAAa3nB,KAAKknB,cAClBa,EAAYviB,EAAeK,QApKT,cAoKsC7F,KAAK4mB,SAE9D5mB,KAAKwC,SAASlI,YAAc0F,KAAKwC,SAASlI,WAAW9B,WAAaoC,KAAKC,cAE1EjE,SAASiF,KAAKmsB,YAAYhoB,KAAKwC,UAGjCxC,KAAKwC,SAASnI,MAAMI,QAAU,QAC9BuF,KAAKwC,SAAS8B,gBAAgB,eAC9BtE,KAAKwC,SAASqB,aAAa,cAAc,GACzC7D,KAAKwC,SAASqB,aAAa,OAAQ,UACnC7D,KAAKwC,SAAS0C,UAAY,EAEtB6iB,IACFA,EAAU7iB,UAAY,GAGpByiB,GACFlsB,EAAOuE,KAAKwC,UAGdxC,KAAKwC,SAAS1H,UAAUuP,IA7LJ,QA+LhBrK,KAAK+H,QAAQyd,OACfxlB,KAAKioB,gBAGP,MAAMC,EAAqB,KACrBloB,KAAK+H,QAAQyd,OACfxlB,KAAKwC,SAASgjB,QAGhBxlB,KAAK0M,kBAAmB,EACxBxM,EAAamB,QAAQrB,KAAKwC,SAtNX,iBAsNkC,CAC/CuI,cAAAA,KAIJ,GAAI4c,EAAY,CACd,MAAMhwB,EAAqBD,EAAiCsI,KAAK4mB,SAEjE1mB,EAAaS,IAAIX,KAAK4mB,QAAS,gBAAiBsB,GAChDzvB,EAAqBuH,KAAK4mB,QAASjvB,QAEnCuwB,IAIJD,gBACE/nB,EAAaC,IAAIvJ,SArOE,oBAsOnBsJ,EAAaQ,GAAG9J,SAtOG,mBAsOsBoI,IACnCpI,WAAaoI,EAAMe,QACnBC,KAAKwC,WAAaxD,EAAMe,QACvBC,KAAKwC,SAASzH,SAASiE,EAAMe,SAChCC,KAAKwC,SAASgjB,UAKpB+B,kBACMvnB,KAAK8mB,SACP5mB,EAAaQ,GAAGV,KAAKwC,SA9OI,2BA8O6BxD,IAChDgB,KAAK+H,QAAQpB,UArQN,WAqQkB3H,EAAMjC,KACjCiC,EAAMqD,iBACNrC,KAAKqN,QACKrN,KAAK+H,QAAQpB,UAxQd,WAwQ0B3H,EAAMjC,KACzCiD,KAAKmoB,+BAITjoB,EAAaC,IAAIH,KAAKwC,SAvPG,4BA2P7BglB,kBACMxnB,KAAK8mB,SACP5mB,EAAaQ,GAAG7I,OA/PA,kBA+PsB,IAAMmI,KAAKsnB,iBAEjDpnB,EAAaC,IAAItI,OAjQD,mBAqQpB+vB,aACE5nB,KAAKwC,SAASnI,MAAMI,QAAU,OAC9BuF,KAAKwC,SAASqB,aAAa,eAAe,GAC1C7D,KAAKwC,SAAS8B,gBAAgB,cAC9BtE,KAAKwC,SAAS8B,gBAAgB,QAC9BtE,KAAK0M,kBAAmB,EACxB1M,KAAKynB,cAAc,KACjB7wB,SAASiF,KAAKf,UAAU2C,OAnQN,cAoQlBuC,KAAKooB,oBACLpoB,KAAKqoB,kBACLnoB,EAAamB,QAAQrB,KAAKwC,SAnRV,qBAuRpB8lB,kBACEtoB,KAAK6mB,UAAUvsB,WAAWgJ,YAAYtD,KAAK6mB,WAC3C7mB,KAAK6mB,UAAY,KAGnBY,cAActrB,GACZ,MAAMwrB,EAAa3nB,KAAKknB,cACxB,GAAIlnB,KAAK8mB,UAAY9mB,KAAK+H,QAAQ2e,SAAU,CAiC1C,GAhCA1mB,KAAK6mB,UAAYjwB,SAAS2xB,cAAc,OACxCvoB,KAAK6mB,UAAU2B,UApRO,iBAsRlBb,GACF3nB,KAAK6mB,UAAU/rB,UAAUuP,IArRT,QAwRlBzT,SAASiF,KAAKmsB,YAAYhoB,KAAK6mB,WAE/B3mB,EAAaQ,GAAGV,KAAKwC,SAnSE,yBAmS6BxD,IAC9CgB,KAAKgnB,qBACPhnB,KAAKgnB,sBAAuB,EAI1BhoB,EAAMe,SAAWf,EAAMypB,gBAIG,WAA1BzoB,KAAK+H,QAAQ2e,SACf1mB,KAAKmoB,6BAELnoB,KAAKqN,UAILsa,GACFlsB,EAAOuE,KAAK6mB,WAGd7mB,KAAK6mB,UAAU/rB,UAAUuP,IA9SP,SAgTbsd,EAEH,YADAxrB,IAIF,MAAMusB,EAA6BhxB,EAAiCsI,KAAK6mB,WAEzE3mB,EAAaS,IAAIX,KAAK6mB,UAAW,gBAAiB1qB,GAClD1D,EAAqBuH,KAAK6mB,UAAW6B,QAChC,IAAK1oB,KAAK8mB,UAAY9mB,KAAK6mB,UAAW,CAC3C7mB,KAAK6mB,UAAU/rB,UAAU2C,OA1TP,QA4TlB,MAAMkrB,EAAiB,KACrB3oB,KAAKsoB,kBACLnsB,KAGF,GAAIwrB,EAAY,CACd,MAAMe,EAA6BhxB,EAAiCsI,KAAK6mB,WACzE3mB,EAAaS,IAAIX,KAAK6mB,UAAW,gBAAiB8B,GAClDlwB,EAAqBuH,KAAK6mB,UAAW6B,QAErCC,SAGFxsB,IAIJ+qB,cACE,OAAOlnB,KAAKwC,SAAS1H,UAAUC,SA/UX,QAkVtBotB,6BAEE,GADkBjoB,EAAamB,QAAQrB,KAAKwC,SAlWlB,0BAmWZb,iBACZ,OAGF,MAAMinB,EAAqB5oB,KAAKwC,SAASyW,aAAeriB,SAASuE,gBAAgBkZ,aAE5EuU,IACH5oB,KAAKwC,SAASnI,MAAM0d,UAAY,UAGlC/X,KAAKwC,SAAS1H,UAAUuP,IA5VF,gBA6VtB,MAAMwe,EAA0BnxB,EAAiCsI,KAAK4mB,SACtE1mB,EAAaC,IAAIH,KAAKwC,SAAU,iBAChCtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiB,KAC/CxC,KAAKwC,SAAS1H,UAAU2C,OAhWJ,gBAiWfmrB,IACH1oB,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiB,KAC/CxC,KAAKwC,SAASnI,MAAM0d,UAAY,KAElCtf,EAAqBuH,KAAKwC,SAAUqmB,MAGxCpwB,EAAqBuH,KAAKwC,SAAUqmB,GACpC7oB,KAAKwC,SAASgjB,QAOhB8B,gBACE,MAAMsB,EAAqB5oB,KAAKwC,SAASyW,aAAeriB,SAASuE,gBAAgBkZ,eAE3ErU,KAAK+mB,oBAAsB6B,IAAuB9sB,KAAakE,KAAK+mB,qBAAuB6B,GAAsB9sB,OACrHkE,KAAKwC,SAASnI,MAAMyuB,YAAiB9oB,KAAKinB,gBAAP,OAGhCjnB,KAAK+mB,qBAAuB6B,IAAuB9sB,MAAckE,KAAK+mB,oBAAsB6B,GAAsB9sB,OACrHkE,KAAKwC,SAASnI,MAAM0uB,aAAkB/oB,KAAKinB,gBAAP,MAIxCmB,oBACEpoB,KAAKwC,SAASnI,MAAMyuB,YAAc,GAClC9oB,KAAKwC,SAASnI,MAAM0uB,aAAe,GAGrC3B,kBACE,MAAMriB,EAAOnO,SAASiF,KAAKmJ,wBAC3BhF,KAAK+mB,mBAAqBtwB,KAAKmc,MAAM7N,EAAKI,KAAOJ,EAAK0J,OAAS5W,OAAOmxB,WACtEhpB,KAAKinB,gBAAkBjnB,KAAKipB,qBAG9B5B,gBACMrnB,KAAK+mB,qBACP/mB,KAAKkpB,sBAnYoB,oDAmY0B,eAAgBC,GAAmBA,EAAkBnpB,KAAKinB,iBAC7GjnB,KAAKkpB,sBAnYqB,cAmY0B,cAAeC,GAAmBA,EAAkBnpB,KAAKinB,iBAC7GjnB,KAAKkpB,sBAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBnpB,KAAKinB,kBAG/FrwB,SAASiF,KAAKf,UAAUuP,IAjZJ,cAoZtB6e,sBAAsBlyB,EAAUoyB,EAAWjtB,GACzCqJ,EAAeC,KAAKzO,GACjBuC,QAAQxC,IACP,GAAIA,IAAYH,SAASiF,MAAQhE,OAAOmxB,WAAajyB,EAAQud,YAActU,KAAKinB,gBAC9E,OAGF,MAAMoC,EAActyB,EAAQsD,MAAM+uB,GAC5BD,EAAkBtxB,OAAOC,iBAAiBf,GAASqyB,GACzDjlB,EAAYC,iBAAiBrN,EAASqyB,EAAWC,GACjDtyB,EAAQsD,MAAM+uB,GAAajtB,EAASnE,OAAOC,WAAWkxB,IAAoB,OAIhFd,kBACEroB,KAAKspB,wBA1ZsB,oDA0Z0B,gBACrDtpB,KAAKspB,wBA1ZuB,cA0Z0B,eACtDtpB,KAAKspB,wBAAwB,OAAQ,gBAGvCA,wBAAwBtyB,EAAUoyB,GAChC5jB,EAAeC,KAAKzO,GAAUuC,QAAQxC,IACpC,MAAM2C,EAAQyK,EAAYU,iBAAiB9N,EAASqyB,QAC/B,IAAV1vB,GAAyB3C,IAAYH,SAASiF,KACvD9E,EAAQsD,MAAM+uB,GAAa,IAE3BjlB,EAAYE,oBAAoBtN,EAASqyB,GACzCryB,EAAQsD,MAAM+uB,GAAa1vB,KAKjCuvB,qBACE,MAAMM,EAAY3yB,SAAS2xB,cAAc,OACzCgB,EAAUf,UAxbwB,0BAyblC5xB,SAASiF,KAAKmsB,YAAYuB,GAC1B,MAAMC,EAAiBD,EAAUvkB,wBAAwB+L,MAAQwY,EAAUjV,YAE3E,OADA1d,SAASiF,KAAKyH,YAAYimB,GACnBC,EAKa7mB,uBAACxJ,EAAQ4R,GAC7B,OAAO/K,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KAjeT,YAkeX,MAAM+H,EAAU,IACXtB,MACAtC,EAAYI,kBAAkBvE,SACX,iBAAX7G,GAAuBA,EAASA,EAAS,IAOtD,GAJKqK,IACHA,EAAO,IAAImjB,GAAM3mB,KAAM+H,IAGH,iBAAX5O,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,GAAQ4R,QAYrB7K,EAAaQ,GAAG9J,SAjec,0BAWD,4BAsdyC,SAAUoI,GAC9E,MAAMe,EAAStI,EAAuBuI,MAEjB,MAAjBA,KAAKsK,SAAoC,SAAjBtK,KAAKsK,SAC/BtL,EAAMqD,iBAGRnC,EAAaS,IAAIZ,EAhfC,gBAgfmBonB,IAC/BA,EAAUxlB,kBAKdzB,EAAaS,IAAIZ,EAvfC,kBAufqB,KACjC3F,EAAU4F,OACZA,KAAKwlB,YAKX,IAAIhiB,EAAO3G,EAAKM,IAAI4C,EAjhBL,YAkhBf,IAAKyD,EAAM,CACT,MAAMrK,EAAS,IACVgL,EAAYI,kBAAkBxE,MAC9BoE,EAAYI,kBAAkBvE,OAGnCwD,EAAO,IAAImjB,GAAM5mB,EAAQ5G,GAG3BqK,EAAKI,OAAO5D,SAUdhE,EAtiBa,QAsiBY2qB,ICzjBzB,MAGM8C,GAAW,KAEf,MAAMC,EAAgB9yB,SAASuE,gBAAgBmZ,YAC/C,OAAO7d,KAAK+S,IAAI3R,OAAOmxB,WAAaU,IAUhCR,GAAwB,CAAClyB,EAAUoyB,EAAWjtB,KAClD,MAAMqtB,EAAiBC,KACvBjkB,EAAeC,KAAKzO,GACjBuC,QAAQxC,IACP,GAAIA,IAAYH,SAASiF,MAAQhE,OAAOmxB,WAAajyB,EAAQud,YAAckV,EACzE,OAGF,MAAMH,EAActyB,EAAQsD,MAAM+uB,GAC5BD,EAAkBtxB,OAAOC,iBAAiBf,GAASqyB,GACzDjlB,EAAYC,iBAAiBrN,EAASqyB,EAAWC,GACjDtyB,EAAQsD,MAAM+uB,GAAajtB,EAASnE,OAAOC,WAAWkxB,IAAoB,QAW1EG,GAA0B,CAACtyB,EAAUoyB,KACzC5jB,EAAeC,KAAKzO,GAAUuC,QAAQxC,IACpC,MAAM2C,EAAQyK,EAAYU,iBAAiB9N,EAASqyB,QAC/B,IAAV1vB,GAAyB3C,IAAYH,SAASiF,KACvD9E,EAAQsD,MAAMsvB,eAAeP,IAE7BjlB,EAAYE,oBAAoBtN,EAASqyB,GACzCryB,EAAQsD,MAAM+uB,GAAa1vB,MCnB3B+M,GAAU,CACdigB,UAAU,EACV/f,UAAU,EACVkQ,QAAQ,GAGJ7P,GAAc,CAClB0f,SAAU,UACV/f,SAAU,UACVkQ,OAAQ,WA0BV,MAAM+S,WAAkBtnB,EACtBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK8mB,UAAW,EAChB9mB,KAAKuI,qBAKW9B,qBAChB,OAAOA,GAGUhE,sBACjB,MAzDa,eA8DfmB,OAAOmH,GACL,OAAO/K,KAAK8mB,SAAW9mB,KAAKqN,OAASrN,KAAKsN,KAAKvC,GAGjDuC,KAAKvC,GACC/K,KAAK8mB,UAIS5mB,EAAamB,QAAQrB,KAAKwC,SA/C5B,oBA+CkD,CAAEuI,cAAAA,IAEtDpJ,mBAId3B,KAAK8mB,UAAW,EAChB9mB,KAAKwC,SAASnI,MAAMK,WAAa,UAE7BsF,KAAK+H,QAAQ2e,UACf9vB,SAASiF,KAAKf,UAAUuP,IA/DG,sBAkExBrK,KAAK+H,QAAQ8O,QD/FT,EAAC9F,EAAQ0Y,QACpB7yB,SAASiF,KAAKxB,MAAMwd,SAAW,SAC/BqR,GAX6B,uCAWiB,eAAgBC,GAAmBA,EAAkBpY,GACnGmY,GAX8B,cAWiB,cAAeC,GAAmBA,EAAkBpY,GACnGmY,GAAsB,OAAQ,eAAgBC,GAAmBA,EAAkBpY,IC4F/E8Y,GAGF7pB,KAAKwC,SAAS1H,UAAUuP,IApEA,sBAqExBrK,KAAKwC,SAAS8B,gBAAgB,eAC9BtE,KAAKwC,SAASqB,aAAa,cAAc,GACzC7D,KAAKwC,SAASqB,aAAa,OAAQ,UACnC7D,KAAKwC,SAAS1H,UAAUuP,IAzEJ,QAiFpBrR,WANyB,KACvBgH,KAAKwC,SAAS1H,UAAU2C,OA3EF,sBA4EtByC,EAAamB,QAAQrB,KAAKwC,SAvEX,qBAuEkC,CAAEuI,cAAAA,IACnD/K,KAAK8pB,uBAAuB9pB,KAAKwC,WAGN9K,EAAiCsI,KAAKwC,YAGrE6K,OACOrN,KAAK8mB,WAIQ5mB,EAAamB,QAAQrB,KAAKwC,SAlF5B,qBAoFFb,mBAId3B,KAAKwC,SAAS1H,UAAUuP,IA9FA,sBA+FxBnK,EAAaC,IAAIvJ,SAvFE,wBAwFnBoJ,KAAKwC,SAASunB,OACd/pB,KAAK8mB,UAAW,EAChB9mB,KAAKwC,SAAS1H,UAAU2C,OAnGJ,QAuHpBzE,WAlByB,KACvBgH,KAAKwC,SAASqB,aAAa,eAAe,GAC1C7D,KAAKwC,SAAS8B,gBAAgB,cAC9BtE,KAAKwC,SAAS8B,gBAAgB,QAC9BtE,KAAKwC,SAASnI,MAAMK,WAAa,SAE7BsF,KAAK+H,QAAQ2e,UACf9vB,SAASiF,KAAKf,UAAU2C,OA7GC,sBAgHtBuC,KAAK+H,QAAQ8O,SDtHtBjgB,SAASiF,KAAKxB,MAAMwd,SAAW,OAC/ByR,GAjC6B,uCAiCmB,gBAChDA,GAjC8B,cAiCmB,eACjDA,GAAwB,OAAQ,iBCuH5BppB,EAAamB,QAAQrB,KAAKwC,SA3GV,uBA4GhBxC,KAAKwC,SAAS1H,UAAU2C,OAnHF,uBAsHK/F,EAAiCsI,KAAKwC,aAKrEwF,WAAW7O,GAOT,OANAA,EAAS,IACJsN,MACAtC,EAAYI,kBAAkBvE,KAAKwC,aAChB,iBAAXrJ,EAAsBA,EAAS,IAE5CF,EAtJS,YAsJaE,EAAQ6N,IACvB7N,EAGT2wB,uBAAuB/yB,GACrBmJ,EAAaC,IAAIvJ,SA9HE,wBA+HnBsJ,EAAaQ,GAAG9J,SA/HG,uBA+HsBoI,IACnCpI,WAAaoI,EAAMe,QACrBhJ,IAAYiI,EAAMe,QACjBhJ,EAAQgE,SAASiE,EAAMe,SACxBhJ,EAAQyuB,UAGZzuB,EAAQyuB,QAGVjd,qBACErI,EAAaQ,GAAGV,KAAKwC,SAxII,6BAEC,gCAsIiD,IAAMxC,KAAKqN,QAEtFnN,EAAaQ,GAAG9J,SAAU,UAAWoI,IAC/BgB,KAAK+H,QAAQpB,UArKJ,WAqKgB3H,EAAMjC,KACjCiD,KAAKqN,SAITnN,EAAaQ,GAAG9J,SAjJU,8BAiJsBoI,IAC9C,MAAMe,EAASyF,EAAeK,QAAQtO,EAAuByH,EAAMe,SAC9DC,KAAKwC,SAASzH,SAASiE,EAAMe,SAAWA,IAAWC,KAAKwC,UAC3DxC,KAAKqN,SAOW1K,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,MAAMC,EAAO3G,EAAKM,IAAI6C,KA1LX,iBA0L8B,IAAI4pB,GAAU5pB,KAAwB,iBAAX7G,EAAsBA,EAAS,IAEnG,GAAsB,iBAAXA,EAAX,CAIA,QAAqB0qB,IAAjBrgB,EAAKrK,IAAyBA,EAAO/B,WAAW,MAAmB,gBAAX+B,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,GAAQ6G,WAWnBE,EAAaQ,GAAG9J,SAlLc,8BAID,gCA8KyC,SAAUoI,GAC9E,MAAMe,EAAStI,EAAuBuI,MAMtC,GAJI,CAAC,IAAK,QAAQ7I,SAAS6I,KAAKsK,UAC9BtL,EAAMqD,iBAGJ1H,EAAWqF,MACb,OAGFE,EAAaS,IAAIZ,EA/LG,sBA+LmB,KAEjC3F,EAAU4F,OACZA,KAAKwlB,UAKT,MAAMwE,EAAexkB,EAAeK,QA5Mb,wCA6MnBmkB,GAAgBA,IAAiBjqB,IAIxBlD,EAAKM,IAAI4C,EAvOP,iBAuO4B,IAAI6pB,GAAU7pB,IAEpD6D,OAAO5D,SAGdE,EAAaQ,GAAG7I,OAzOa,6BAyOgB,KAC3C2N,EAAeC,KAxNK,mBAwNelM,QAAQ0wB,IAAOptB,EAAKM,IAAI8sB,EA7O5C,iBA6O6D,IAAIL,GAAUK,IAAK3c,UASjGtR,EAvPa,YAuPY4tB,IC7QzB,MAAMM,GAAW,IAAI9rB,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI+rB,GAAmB,6DAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKpb,SAASnV,cAE/B,GAAIwwB,EAAqBpzB,SAASqzB,GAChC,OAAIN,GAASjtB,IAAIutB,IACR/pB,QAAQ0pB,GAAiBlwB,KAAKqwB,EAAKG,YAAcL,GAAiBnwB,KAAKqwB,EAAKG,YAMvF,MAAMC,EAASH,EAAqB7lB,OAAOimB,GAAaA,aAAqB3wB,QAG7E,IAAK,IAAI6E,EAAI,EAAGC,EAAM4rB,EAAO3rB,OAAQF,EAAIC,EAAKD,IAC5C,GAAI6rB,EAAO7rB,GAAG5E,KAAKuwB,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASI,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW9rB,OACd,OAAO8rB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAInzB,OAAOozB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB9xB,OAAOC,KAAKwxB,GAC5B/a,EAAW,GAAGrK,UAAUslB,EAAgBnvB,KAAKiE,iBAAiB,MAEpE,IAAK,IAAIjB,EAAI,EAAGC,EAAMiR,EAAShR,OAAQF,EAAIC,EAAKD,IAAK,CACnD,MAAMorB,EAAKla,EAASlR,GACdusB,EAASnB,EAAG/a,SAASnV,cAE3B,IAAKoxB,EAAch0B,SAASi0B,GAAS,CACnCnB,EAAG3vB,WAAWgJ,YAAY2mB,GAE1B,SAGF,MAAMoB,EAAgB,GAAG3lB,UAAUukB,EAAGzlB,YAChC8mB,EAAoB,GAAG5lB,OAAOolB,EAAU,MAAQ,GAAIA,EAAUM,IAAW,IAE/EC,EAAc9xB,QAAQ+wB,IACfD,GAAiBC,EAAMgB,IAC1BrB,EAAG3lB,gBAAgBgmB,EAAKpb,YAK9B,OAAO8b,EAAgBnvB,KAAK0vB,UCzF9B,MAIMC,GAAqB,IAAIxxB,OAAQ,wBAA6B,KAC9DyxB,GAAwB,IAAIrtB,IAAI,CAAC,WAAY,YAAa,eAE1D4I,GAAc,CAClB0kB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPvqB,QAAS,SACTwqB,MAAO,kBACPnT,KAAM,UACN1hB,SAAU,mBACV8X,UAAW,oBACXhK,OAAQ,0BACR2I,UAAW,2BACXgP,mBAAoB,QACpB5C,SAAU,mBACViS,YAAa,oBACbC,SAAU,UACVhB,WAAY,kBACZD,UAAW,SACXnG,aAAc,0BAGVqH,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOrwB,IAAU,OAAS,QAC1BswB,OAAQ,SACRC,KAAMvwB,IAAU,QAAU,QAGtB2K,GAAU,CACdilB,WAAW,EACXC,SAAU,+GAIVtqB,QAAS,cACTuqB,MAAO,GACPC,MAAO,EACPnT,MAAM,EACN1hB,UAAU,EACV8X,UAAW,MACXhK,OAAQ,CAAC,EAAG,GACZ2I,WAAW,EACXgP,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C5C,SAAU,kBACViS,YAAa,GACbC,UAAU,EACVhB,WAAY,KACZD,UDjC8B,CAE9BwB,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BvQ,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BwQ,KAAM,GACNvQ,EAAG,GACHwQ,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJtuB,EAAG,GACHuuB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICGJrJ,aAAc,MAGVtsB,GAAQ,CACZ41B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAuBf,MAAMC,WAAgBrsB,EACpBC,YAAYxL,EAASoC,GACnB,QAAsB,IAAXksB,GACT,MAAM,IAAInrB,UAAU,+DAGtBoN,MAAMvQ,GAGNiJ,KAAK4uB,YAAa,EAClB5uB,KAAK6uB,SAAW,EAChB7uB,KAAK8uB,YAAc,GACnB9uB,KAAK+uB,eAAiB,GACtB/uB,KAAK6kB,QAAU,KAGf7kB,KAAK7G,OAAS6G,KAAKgI,WAAW7O,GAC9B6G,KAAKgvB,IAAM,KAEXhvB,KAAKivB,gBAKWxoB,qBAChB,OAAOA,GAGMyoB,kBACb,MAxHS,UA2HQzsB,sBACjB,MA3Ha,aA8HCpK,mBACd,OAAOA,GAGW82B,uBAClB,MAlIe,cAqIKnoB,yBACpB,OAAOA,GAKTooB,SACEpvB,KAAK4uB,YAAa,EAGpBS,UACErvB,KAAK4uB,YAAa,EAGpBU,gBACEtvB,KAAK4uB,YAAc5uB,KAAK4uB,WAG1BhrB,OAAO5E,GACL,GAAKgB,KAAK4uB,WAIV,GAAI5vB,EAAO,CACT,MAAMknB,EAAUlmB,KAAKuvB,6BAA6BvwB,GAElDknB,EAAQ6I,eAAexI,OAASL,EAAQ6I,eAAexI,MAEnDL,EAAQsJ,uBACVtJ,EAAQuJ,OAAO,KAAMvJ,GAErBA,EAAQwJ,OAAO,KAAMxJ,OAElB,CACL,GAAIlmB,KAAK2vB,gBAAgB70B,UAAUC,SAhGjB,QAkGhB,YADAiF,KAAK0vB,OAAO,KAAM1vB,MAIpBA,KAAKyvB,OAAO,KAAMzvB,OAItB0C,UACEwH,aAAalK,KAAK6uB,UAElB3uB,EAAaC,IAAIH,KAAKwC,SAAUxC,KAAKuC,YAAY4sB,WACjDjvB,EAAaC,IAAIH,KAAKwC,SAASY,QAAS,UAAwB,gBAAiBpD,KAAK4vB,mBAElF5vB,KAAKgvB,KAAOhvB,KAAKgvB,IAAI10B,YACvB0F,KAAKgvB,IAAI10B,WAAWgJ,YAAYtD,KAAKgvB,KAGvChvB,KAAK4uB,WAAa,KAClB5uB,KAAK6uB,SAAW,KAChB7uB,KAAK8uB,YAAc,KACnB9uB,KAAK+uB,eAAiB,KAClB/uB,KAAK6kB,SACP7kB,KAAK6kB,QAAQf,UAGf9jB,KAAK6kB,QAAU,KACf7kB,KAAK7G,OAAS,KACd6G,KAAKgvB,IAAM,KACX1nB,MAAM5E,UAGR4K,OACE,GAAoC,SAAhCtN,KAAKwC,SAASnI,MAAMI,QACtB,MAAM,IAAIo1B,MAAM,uCAGlB,IAAM7vB,KAAK8vB,kBAAmB9vB,KAAK4uB,WACjC,OAGF,MAAMzH,EAAYjnB,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM81B,MACvE4B,EAAa70B,EAAe8E,KAAKwC,UACjCwtB,EAA4B,OAAfD,EACjB/vB,KAAKwC,SAAS6M,cAAclU,gBAAgBJ,SAASiF,KAAKwC,UAC1DutB,EAAWh1B,SAASiF,KAAKwC,UAE3B,GAAI2kB,EAAUxlB,mBAAqBquB,EACjC,OAGF,MAAMhB,EAAMhvB,KAAK2vB,gBACXM,EAAQ15B,EAAOyJ,KAAKuC,YAAY2sB,MAEtCF,EAAInrB,aAAa,KAAMosB,GACvBjwB,KAAKwC,SAASqB,aAAa,mBAAoBosB,GAE/CjwB,KAAKkwB,aAEDlwB,KAAK7G,OAAOuyB,WACdsD,EAAIl0B,UAAUuP,IA/JI,QAkKpB,MAAMyE,EAA6C,mBAA1B9O,KAAK7G,OAAO2V,UACnC9O,KAAK7G,OAAO2V,UAAUjV,KAAKmG,KAAMgvB,EAAKhvB,KAAKwC,UAC3CxC,KAAK7G,OAAO2V,UAERqhB,EAAanwB,KAAKowB,eAAethB,GACvC9O,KAAKqwB,oBAAoBF,GAEzB,MAAM1iB,EAAYzN,KAAKswB,gBACvBzzB,EAAKC,IAAIkyB,EAAKhvB,KAAKuC,YAAYE,SAAUzC,MAEpCA,KAAKwC,SAAS6M,cAAclU,gBAAgBJ,SAASiF,KAAKgvB,OAC7DvhB,EAAUua,YAAYgH,GACtB9uB,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAMg2B,WAGzDruB,KAAK6kB,QACP7kB,KAAK6kB,QAAQ3N,SAEblX,KAAK6kB,QAAUQ,GAAoBrlB,KAAKwC,SAAUwsB,EAAKhvB,KAAKslB,iBAAiB6K,IAG/EnB,EAAIl0B,UAAUuP,IArLM,QAuLpB,MAAMyhB,EAAiD,mBAA5B9rB,KAAK7G,OAAO2yB,YAA6B9rB,KAAK7G,OAAO2yB,cAAgB9rB,KAAK7G,OAAO2yB,YACxGA,GACFkD,EAAIl0B,UAAUuP,OAAOyhB,EAAYz0B,MAAM,MAOrC,iBAAkBT,SAASuE,iBAC7B,GAAGuK,UAAU9O,SAASiF,KAAKiK,UAAUvM,QAAQxC,IAC3CmJ,EAAaQ,GAAG3J,EAAS,apE7Gd,iBoEiHf,MAAMw5B,EAAW,KACf,MAAMC,EAAiBxwB,KAAK8uB,YAE5B9uB,KAAK8uB,YAAc,KACnB5uB,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM+1B,OAvMzC,QAyMdoC,GACFxwB,KAAK0vB,OAAO,KAAM1vB,OAItB,GAAIA,KAAKgvB,IAAIl0B,UAAUC,SAnNH,QAmN8B,CAChD,MAAMpD,EAAqBD,EAAiCsI,KAAKgvB,KACjE9uB,EAAaS,IAAIX,KAAKgvB,IAAK,gBAAiBuB,GAC5C93B,EAAqBuH,KAAKgvB,IAAKr3B,QAE/B44B,IAIJljB,OACE,IAAKrN,KAAK6kB,QACR,OAGF,MAAMmK,EAAMhvB,KAAK2vB,gBACXY,EAAW,KACXvwB,KAAKwvB,yBA/NU,SAmOfxvB,KAAK8uB,aAAoCE,EAAI10B,YAC/C00B,EAAI10B,WAAWgJ,YAAY0rB,GAG7BhvB,KAAKywB,iBACLzwB,KAAKwC,SAAS8B,gBAAgB,oBAC9BpE,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM61B,QAEvDluB,KAAK6kB,UACP7kB,KAAK6kB,QAAQf,UACb9jB,KAAK6kB,QAAU,QAKnB,IADkB3kB,EAAamB,QAAQrB,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAM41B,MAC/DtsB,iBAAd,CAiBA,GAbAqtB,EAAIl0B,UAAU2C,OAxPM,QA4PhB,iBAAkB7G,SAASuE,iBAC7B,GAAGuK,UAAU9O,SAASiF,KAAKiK,UACxBvM,QAAQxC,GAAWmJ,EAAaC,IAAIpJ,EAAS,YAAayE,IAG/DwE,KAAK+uB,eAAL,OAAqC,EACrC/uB,KAAK+uB,eAAL,OAAqC,EACrC/uB,KAAK+uB,eAAL,OAAqC,EAEjC/uB,KAAKgvB,IAAIl0B,UAAUC,SAvQH,QAuQ8B,CAChD,MAAMpD,EAAqBD,EAAiCs3B,GAE5D9uB,EAAaS,IAAIquB,EAAK,gBAAiBuB,GACvC93B,EAAqBu2B,EAAKr3B,QAE1B44B,IAGFvwB,KAAK8uB,YAAc,IAGrB5X,SACuB,OAAjBlX,KAAK6kB,SACP7kB,KAAK6kB,QAAQ3N,SAMjB4Y,gBACE,OAAOrvB,QAAQT,KAAK0wB,YAGtBf,gBACE,GAAI3vB,KAAKgvB,IACP,OAAOhvB,KAAKgvB,IAGd,MAAMj4B,EAAUH,SAAS2xB,cAAc,OAIvC,OAHAxxB,EAAQw0B,UAAYvrB,KAAK7G,OAAOwyB,SAEhC3rB,KAAKgvB,IAAMj4B,EAAQ+O,SAAS,GACrB9F,KAAKgvB,IAGdkB,aACE,MAAMlB,EAAMhvB,KAAK2vB,gBACjB3vB,KAAK2wB,kBAAkBnrB,EAAeK,QAtSX,iBAsS2CmpB,GAAMhvB,KAAK0wB,YACjF1B,EAAIl0B,UAAU2C,OA9SM,OAEA,QA+StBkzB,kBAAkB55B,EAAS65B,GACzB,GAAgB,OAAZ75B,EAIJ,MAAuB,iBAAZ65B,GAAwBt4B,EAAUs4B,IACvCA,EAAQziB,SACVyiB,EAAUA,EAAQ,SAIhB5wB,KAAK7G,OAAOuf,KACVkY,EAAQt2B,aAAevD,IACzBA,EAAQw0B,UAAY,GACpBx0B,EAAQixB,YAAY4I,IAGtB75B,EAAQ85B,YAAcD,EAAQC,mBAM9B7wB,KAAK7G,OAAOuf,MACV1Y,KAAK7G,OAAO4yB,WACd6E,EAAUhG,GAAagG,EAAS5wB,KAAK7G,OAAO2xB,UAAW9qB,KAAK7G,OAAO4xB,aAGrEh0B,EAAQw0B,UAAYqF,GAEpB75B,EAAQ85B,YAAcD,GAI1BF,WACE,IAAI9E,EAAQ5rB,KAAKwC,SAASvL,aAAa,0BAQvC,OANK20B,IACHA,EAAqC,mBAAtB5rB,KAAK7G,OAAOyyB,MACzB5rB,KAAK7G,OAAOyyB,MAAM/xB,KAAKmG,KAAKwC,UAC5BxC,KAAK7G,OAAOyyB,OAGTA,EAGTkF,iBAAiBX,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTZ,6BAA6BvwB,EAAOknB,GAClC,MAAM6K,EAAU/wB,KAAKuC,YAAYE,SAQjC,OAPAyjB,EAAUA,GAAWrpB,EAAKM,IAAI6B,EAAMiB,eAAgB8wB,MAGlD7K,EAAU,IAAIlmB,KAAKuC,YAAYvD,EAAMiB,eAAgBD,KAAKgxB,sBAC1Dn0B,EAAKC,IAAIkC,EAAMiB,eAAgB8wB,EAAS7K,IAGnCA,EAGTL,aACE,MAAM/gB,OAAEA,GAAW9E,KAAK7G,OAExB,MAAsB,iBAAX2L,EACFA,EAAOzN,MAAM,KAAKqrB,IAAI3e,GAAO/L,OAAOsT,SAASvH,EAAK,KAGrC,mBAAXe,EACFghB,GAAchhB,EAAOghB,EAAY9lB,KAAKwC,UAGxCsC,EAGTwgB,iBAAiB6K,GACf,MAAMpK,EAAwB,CAC5BjX,UAAWqhB,EACX5O,UAAW,CACT,CACEtlB,KAAM,OACNqU,QAAS,CACP6J,aAAa,EACbsC,mBAAoBzc,KAAK7G,OAAOsjB,qBAGpC,CACExgB,KAAM,SACNqU,QAAS,CACPxL,OAAQ9E,KAAK6lB,eAGjB,CACE5pB,KAAM,kBACNqU,QAAS,CACPuJ,SAAU7Z,KAAK7G,OAAO0gB,WAG1B,CACE5d,KAAM,QACNqU,QAAS,CACPvZ,QAAU,IAAGiJ,KAAKuC,YAAY2sB,eAGlC,CACEjzB,KAAM,WACN0T,SAAS,EACTC,MAAO,aACPtT,GAAIkH,GAAQxD,KAAKixB,6BAA6BztB,KAGlDugB,cAAevgB,IACTA,EAAK8M,QAAQxB,YAActL,EAAKsL,WAClC9O,KAAKixB,6BAA6BztB,KAKxC,MAAO,IACFuiB,KACqC,mBAA7B/lB,KAAK7G,OAAOwrB,aAA8B3kB,KAAK7G,OAAOwrB,aAAaoB,GAAyB/lB,KAAK7G,OAAOwrB,cAIvH0L,oBAAoBF,GAClBnwB,KAAK2vB,gBAAgB70B,UAAUuP,IAAK,cAAkBrK,KAAK8wB,iBAAiBX,IAG9EG,gBACE,OAA8B,IAA1BtwB,KAAK7G,OAAOsU,UACP7W,SAASiF,KAGdvD,EAAU0H,KAAK7G,OAAOsU,WACjBzN,KAAK7G,OAAOsU,UAGdjI,EAAeK,QAAQ7F,KAAK7G,OAAOsU,WAG5C2iB,eAAethB,GACb,OAAOkd,GAAcld,EAAU3U,eAGjC80B,gBACmBjvB,KAAK7G,OAAOkI,QAAQhK,MAAM,KAElCkC,QAAQ8H,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGV,KAAKwC,SAAUxC,KAAKuC,YAAYlK,MAAMi2B,MAAOtuB,KAAK7G,OAAOnC,SAAUgI,GAASgB,KAAK4D,OAAO5E,SACnG,GAtcU,WAscNqC,EAA4B,CACrC,MAAM6vB,EA1cQ,UA0cE7vB,EACdrB,KAAKuC,YAAYlK,MAAMo2B,WACvBzuB,KAAKuC,YAAYlK,MAAMk2B,QACnB4C,EA7cQ,UA6cG9vB,EACfrB,KAAKuC,YAAYlK,MAAMq2B,WACvB1uB,KAAKuC,YAAYlK,MAAMm2B,SAEzBtuB,EAAaQ,GAAGV,KAAKwC,SAAU0uB,EAASlxB,KAAK7G,OAAOnC,SAAUgI,GAASgB,KAAKyvB,OAAOzwB,IACnFkB,EAAaQ,GAAGV,KAAKwC,SAAU2uB,EAAUnxB,KAAK7G,OAAOnC,SAAUgI,GAASgB,KAAK0vB,OAAO1wB,OAIxFgB,KAAK4vB,kBAAoB,KACnB5vB,KAAKwC,UACPxC,KAAKqN,QAITnN,EAAaQ,GAAGV,KAAKwC,SAASY,QAAS,UAAwB,gBAAiBpD,KAAK4vB,mBAEjF5vB,KAAK7G,OAAOnC,SACdgJ,KAAK7G,OAAS,IACT6G,KAAK7G,OACRkI,QAAS,SACTrK,SAAU,IAGZgJ,KAAKoxB,YAITA,YACE,MAAMxF,EAAQ5rB,KAAKwC,SAASvL,aAAa,SACnCo6B,SAA2BrxB,KAAKwC,SAASvL,aAAa,2BAExD20B,GAA+B,WAAtByF,KACXrxB,KAAKwC,SAASqB,aAAa,yBAA0B+nB,GAAS,KAC1DA,GAAU5rB,KAAKwC,SAASvL,aAAa,eAAkB+I,KAAKwC,SAASquB,aACvE7wB,KAAKwC,SAASqB,aAAa,aAAc+nB,GAG3C5rB,KAAKwC,SAASqB,aAAa,QAAS,KAIxC4rB,OAAOzwB,EAAOknB,GACZA,EAAUlmB,KAAKuvB,6BAA6BvwB,EAAOknB,GAE/ClnB,IACFknB,EAAQ6I,eACS,YAAf/vB,EAAMoB,KA3fQ,QADA,UA6fZ,GAGF8lB,EAAQyJ,gBAAgB70B,UAAUC,SAvgBlB,SAEC,SAqgB8CmrB,EAAQ4I,YACzE5I,EAAQ4I,YAtgBW,QA0gBrB5kB,aAAagc,EAAQ2I,UAErB3I,EAAQ4I,YA5gBa,OA8gBhB5I,EAAQ/sB,OAAO0yB,OAAU3F,EAAQ/sB,OAAO0yB,MAAMve,KAKnD4Y,EAAQ2I,SAAW71B,WAAW,KAnhBT,SAohBfktB,EAAQ4I,aACV5I,EAAQ5Y,QAET4Y,EAAQ/sB,OAAO0yB,MAAMve,MARtB4Y,EAAQ5Y,QAWZoiB,OAAO1wB,EAAOknB,GACZA,EAAUlmB,KAAKuvB,6BAA6BvwB,EAAOknB,GAE/ClnB,IACFknB,EAAQ6I,eACS,aAAf/vB,EAAMoB,KAzhBQ,QADA,SA2hBZ8lB,EAAQ1jB,SAASzH,SAASiE,EAAM+L,gBAGlCmb,EAAQsJ,yBAIZtlB,aAAagc,EAAQ2I,UAErB3I,EAAQ4I,YAxiBY,MA0iBf5I,EAAQ/sB,OAAO0yB,OAAU3F,EAAQ/sB,OAAO0yB,MAAMxe,KAKnD6Y,EAAQ2I,SAAW71B,WAAW,KA/iBV,QAgjBdktB,EAAQ4I,aACV5I,EAAQ7Y,QAET6Y,EAAQ/sB,OAAO0yB,MAAMxe,MARtB6Y,EAAQ7Y,QAWZmiB,uBACE,IAAK,MAAMnuB,KAAWrB,KAAK+uB,eACzB,GAAI/uB,KAAK+uB,eAAe1tB,GACtB,OAAO,EAIX,OAAO,EAGT2G,WAAW7O,GACT,MAAMm4B,EAAiBntB,EAAYI,kBAAkBvE,KAAKwC,UAuC1D,OArCAnJ,OAAOC,KAAKg4B,GAAgB/3B,QAAQg4B,IAC9B9F,GAAsBxuB,IAAIs0B,WACrBD,EAAeC,KAItBp4B,GAAsC,iBAArBA,EAAOsU,WAA0BtU,EAAOsU,UAAUU,SACrEhV,EAAOsU,UAAYtU,EAAOsU,UAAU,IASV,iBAN5BtU,EAAS,IACJ6G,KAAKuC,YAAYkE,WACjB6qB,KACmB,iBAAXn4B,GAAuBA,EAASA,EAAS,KAGpC0yB,QAChB1yB,EAAO0yB,MAAQ,CACbve,KAAMnU,EAAO0yB,MACbxe,KAAMlU,EAAO0yB,QAIW,iBAAjB1yB,EAAOyyB,QAChBzyB,EAAOyyB,MAAQzyB,EAAOyyB,MAAMhyB,YAGA,iBAAnBT,EAAOy3B,UAChBz3B,EAAOy3B,QAAUz3B,EAAOy3B,QAAQh3B,YAGlCX,EA9qBS,UA8qBaE,EAAQ6G,KAAKuC,YAAYyE,aAE3C7N,EAAO4yB,WACT5yB,EAAOwyB,SAAWf,GAAazxB,EAAOwyB,SAAUxyB,EAAO2xB,UAAW3xB,EAAO4xB,aAGpE5xB,EAGT63B,qBACE,MAAM73B,EAAS,GAEf,GAAI6G,KAAK7G,OACP,IAAK,MAAM4D,KAAOiD,KAAK7G,OACjB6G,KAAKuC,YAAYkE,QAAQ1J,KAASiD,KAAK7G,OAAO4D,KAChD5D,EAAO4D,GAAOiD,KAAK7G,OAAO4D,IAKhC,OAAO5D,EAGTs3B,iBACE,MAAMzB,EAAMhvB,KAAK2vB,gBACX6B,EAAWxC,EAAI/3B,aAAa,SAAS6C,MAAM0xB,IAChC,OAAbgG,GAAqBA,EAASzyB,OAAS,GACzCyyB,EAAS9O,IAAI+O,GAASA,EAAMn6B,QACzBiC,QAAQm4B,GAAU1C,EAAIl0B,UAAU2C,OAAOi0B,IAI9CT,6BAA6BnL,GAC3B,MAAMhW,MAAEA,GAAUgW,EAEbhW,IAIL9P,KAAKgvB,IAAMlf,EAAMC,SAASM,OAC1BrQ,KAAKywB,iBACLzwB,KAAKqwB,oBAAoBrwB,KAAKowB,eAAetgB,EAAMhB,aAK/BnM,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KA7tBT,cA8tBX,MAAM+H,EAA4B,iBAAX5O,GAAuBA,EAE9C,IAAKqK,IAAQ,eAAevJ,KAAKd,MAI5BqK,IACHA,EAAO,IAAImrB,GAAQ3uB,KAAM+H,IAGL,iBAAX5O,GAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAab6C,EA3vBa,UA2vBY2yB,IC7wBzB,MAIMnD,GAAqB,IAAIxxB,OAAQ,wBAA6B,KAE9DyM,GAAU,IACXkoB,GAAQloB,QACXqI,UAAW,QACXhK,OAAQ,CAAC,EAAG,GACZzD,QAAS,QACTuvB,QAAS,GACTjF,SAAU,+IAON3kB,GAAc,IACf2nB,GAAQ3nB,YACX4pB,QAAS,6BAGLv4B,GAAQ,CACZ41B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAef,MAAMiD,WAAgBhD,GAGFloB,qBAChB,OAAOA,GAGMyoB,kBACb,MAzDS,UA4DQzsB,sBACjB,MA5Da,aA+DCpK,mBACd,OAAOA,GAGW82B,uBAClB,MAnEe,cAsEKnoB,yBACpB,OAAOA,GAKT8oB,gBACE,OAAO9vB,KAAK0wB,YAAc1wB,KAAK4xB,cAGjC1B,aACE,MAAMlB,EAAMhvB,KAAK2vB,gBAGjB3vB,KAAK2wB,kBAAkBnrB,EAAeK,QA9CnB,kBA8C2CmpB,GAAMhvB,KAAK0wB,YACzE,IAAIE,EAAU5wB,KAAK4xB,cACI,mBAAZhB,IACTA,EAAUA,EAAQ/2B,KAAKmG,KAAKwC,WAG9BxC,KAAK2wB,kBAAkBnrB,EAAeK,QAnDjB,gBAmD2CmpB,GAAM4B,GAEtE5B,EAAIl0B,UAAU2C,OAzDM,OACA,QA6DtB4yB,oBAAoBF,GAClBnwB,KAAK2vB,gBAAgB70B,UAAUuP,IAAK,cAAkBrK,KAAK8wB,iBAAiBX,IAG9EyB,cACE,OAAO5xB,KAAKwC,SAASvL,aAAa,oBAAsB+I,KAAK7G,OAAOy3B,QAGtEH,iBACE,MAAMzB,EAAMhvB,KAAK2vB,gBACX6B,EAAWxC,EAAI/3B,aAAa,SAAS6C,MAAM0xB,IAChC,OAAbgG,GAAqBA,EAASzyB,OAAS,GACzCyyB,EAAS9O,IAAI+O,GAASA,EAAMn6B,QACzBiC,QAAQm4B,GAAU1C,EAAIl0B,UAAU2C,OAAOi0B,IAMxB/uB,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KAvHT,cAwHX,MAAM+H,EAA4B,iBAAX5O,EAAsBA,EAAS,KAEtD,IAAKqK,IAAQ,eAAevJ,KAAKd,MAI5BqK,IACHA,EAAO,IAAImuB,GAAQ3xB,KAAM+H,GACzBlL,EAAKC,IAAIkD,KAhIA,aAgIgBwD,IAGL,iBAAXrK,GAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAab6C,EAtJa,UAsJY21B,IC9IzB,MAKMlrB,GAAU,CACd3B,OAAQ,GACR+sB,OAAQ,OACR9xB,OAAQ,IAGJiH,GAAc,CAClBlC,OAAQ,SACR+sB,OAAQ,SACR9xB,OAAQ,oBA2BV,MAAM+xB,WAAkBxvB,EACtBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GACNiJ,KAAK+xB,eAA2C,SAA1B/xB,KAAKwC,SAAS8H,QAAqBzS,OAASmI,KAAKwC,SACvExC,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAKiN,UAAa,GAAEjN,KAAK+H,QAAQhI,qBAAiCC,KAAK+H,QAAQhI,4BAAkCC,KAAK+H,QAAQhI,wBAC9HC,KAAKgyB,SAAW,GAChBhyB,KAAKiyB,SAAW,GAChBjyB,KAAKkyB,cAAgB,KACrBlyB,KAAKmyB,cAAgB,EAErBjyB,EAAaQ,GAAGV,KAAK+xB,eAlCH,sBAkCiC,IAAM/xB,KAAKoyB,YAE9DpyB,KAAKqyB,UACLryB,KAAKoyB,WAKW3rB,qBAChB,OAAOA,GAGUhE,sBACjB,MAhEa,eAqEf4vB,UACE,MAAMC,EAAatyB,KAAK+xB,iBAAmB/xB,KAAK+xB,eAAel6B,OAvC7C,SACE,WA0Cd06B,EAAuC,SAAxBvyB,KAAK+H,QAAQ8pB,OAChCS,EACAtyB,KAAK+H,QAAQ8pB,OAETW,EA9Cc,aA8CDD,EACjBvyB,KAAKyyB,gBACL,EAEFzyB,KAAKgyB,SAAW,GAChBhyB,KAAKiyB,SAAW,GAChBjyB,KAAKmyB,cAAgBnyB,KAAK0yB,mBAEVltB,EAAeC,KAAKzF,KAAKiN,WAEjCyV,IAAI3rB,IACV,MAAM47B,EAAiBp7B,EAAuBR,GACxCgJ,EAAS4yB,EAAiBntB,EAAeK,QAAQ8sB,GAAkB,KAEzE,GAAI5yB,EAAQ,CACV,MAAM6yB,EAAY7yB,EAAOiF,wBACzB,GAAI4tB,EAAU7hB,OAAS6hB,EAAU5hB,OAC/B,MAAO,CACL7M,EAAYouB,GAAcxyB,GAAQkF,IAAMutB,EACxCG,GAKN,OAAO,OAENjuB,OAAOmuB,GAAQA,GACf/W,KAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IACxBziB,QAAQs5B,IACP7yB,KAAKgyB,SAAS7rB,KAAK0sB,EAAK,IACxB7yB,KAAKiyB,SAAS9rB,KAAK0sB,EAAK,MAI9BnwB,UACE4E,MAAM5E,UACNxC,EAAaC,IAAIH,KAAK+xB,eAjHP,iBAmHf/xB,KAAK+xB,eAAiB,KACtB/xB,KAAK+H,QAAU,KACf/H,KAAKiN,UAAY,KACjBjN,KAAKgyB,SAAW,KAChBhyB,KAAKiyB,SAAW,KAChBjyB,KAAKkyB,cAAgB,KACrBlyB,KAAKmyB,cAAgB,KAKvBnqB,WAAW7O,GAMT,GAA6B,iBAL7BA,EAAS,IACJsN,MACmB,iBAAXtN,GAAuBA,EAASA,EAAS,KAGpC4G,QAAuBzH,EAAUa,EAAO4G,QAAS,CACjE,IAAI6M,GAAEA,GAAOzT,EAAO4G,OACf6M,IACHA,EAAKrW,EAzIA,aA0IL4C,EAAO4G,OAAO6M,GAAKA,GAGrBzT,EAAO4G,OAAU,IAAG6M,EAKtB,OAFA3T,EAhJS,YAgJaE,EAAQ6N,IAEvB7N,EAGTs5B,gBACE,OAAOzyB,KAAK+xB,iBAAmBl6B,OAC7BmI,KAAK+xB,eAAeta,YACpBzX,KAAK+xB,eAAe7sB,UAGxBwtB,mBACE,OAAO1yB,KAAK+xB,eAAe9Y,cAAgBxiB,KAAKic,IAC9C9b,SAASiF,KAAKod,aACdriB,SAASuE,gBAAgB8d,cAI7B6Z,mBACE,OAAO9yB,KAAK+xB,iBAAmBl6B,OAC7BA,OAAOk7B,YACP/yB,KAAK+xB,eAAe/sB,wBAAwBgM,OAGhDohB,WACE,MAAMltB,EAAYlF,KAAKyyB,gBAAkBzyB,KAAK+H,QAAQjD,OAChDmU,EAAejZ,KAAK0yB,mBACpBM,EAAYhzB,KAAK+H,QAAQjD,OAASmU,EAAejZ,KAAK8yB,mBAM5D,GAJI9yB,KAAKmyB,gBAAkBlZ,GACzBjZ,KAAKqyB,UAGHntB,GAAa8tB,EAAjB,CACE,MAAMjzB,EAASC,KAAKiyB,SAASjyB,KAAKiyB,SAASlzB,OAAS,GAEhDiB,KAAKkyB,gBAAkBnyB,GACzBC,KAAKizB,UAAUlzB,OAJnB,CAUA,GAAIC,KAAKkyB,eAAiBhtB,EAAYlF,KAAKgyB,SAAS,IAAMhyB,KAAKgyB,SAAS,GAAK,EAG3E,OAFAhyB,KAAKkyB,cAAgB,UACrBlyB,KAAKkzB,SAIP,IAAK,IAAIr0B,EAAImB,KAAKgyB,SAASjzB,OAAQF,KACVmB,KAAKkyB,gBAAkBlyB,KAAKiyB,SAASpzB,IACxDqG,GAAalF,KAAKgyB,SAASnzB,UACM,IAAzBmB,KAAKgyB,SAASnzB,EAAI,IAAsBqG,EAAYlF,KAAKgyB,SAASnzB,EAAI,KAGhFmB,KAAKizB,UAAUjzB,KAAKiyB,SAASpzB,KAKnCo0B,UAAUlzB,GACRC,KAAKkyB,cAAgBnyB,EAErBC,KAAKkzB,SAEL,MAAMC,EAAUnzB,KAAKiN,UAAU5V,MAAM,KAClCqrB,IAAI1rB,GAAa,GAAEA,qBAA4B+I,OAAY/I,WAAkB+I,OAE1EqzB,EAAO5tB,EAAeK,QAAQstB,EAAQE,KAAK,MAE7CD,EAAKt4B,UAAUC,SAjMU,kBAkM3ByK,EAAeK,QAzLY,mBAyLsButB,EAAKhwB,QA1LlC,cA2LjBtI,UAAUuP,IAlMO,UAoMpB+oB,EAAKt4B,UAAUuP,IApMK,YAuMpB+oB,EAAKt4B,UAAUuP,IAvMK,UAyMpB7E,EAAeS,QAAQmtB,EAtMG,qBAuMvB75B,QAAQ+5B,IAGP9tB,EAAeY,KAAKktB,EAAY,+BAC7B/5B,QAAQs5B,GAAQA,EAAK/3B,UAAUuP,IA9MlB,WAiNhB7E,EAAeY,KAAKktB,EA5MH,aA6Md/5B,QAAQg6B,IACP/tB,EAAeM,SAASytB,EA/MX,aAgNVh6B,QAAQs5B,GAAQA,EAAK/3B,UAAUuP,IApNtB,gBAyNtBnK,EAAamB,QAAQrB,KAAK+xB,eA9NN,wBA8NsC,CACxDhnB,cAAehL,IAInBmzB,SACE1tB,EAAeC,KAAKzF,KAAKiN,WACtBvI,OAAO0K,GAAQA,EAAKtU,UAAUC,SAhOX,WAiOnBxB,QAAQ6V,GAAQA,EAAKtU,UAAU2C,OAjOZ,WAsOFkF,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KA7PT,gBAoQX,GAJKwD,IACHA,EAAO,IAAIsuB,GAAU9xB,KAHW,iBAAX7G,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAYb+G,EAAaQ,GAAG7I,OAnQa,6BAmQgB,KAC3C2N,EAAeC,KA/PS,0BAgQrBlM,QAAQi6B,GAAO,IAAI1B,GAAU0B,EAAKrvB,EAAYI,kBAAkBivB,OAUrEx3B,EAlSa,YAkSY81B,ICpQzB,MAAM2B,WAAYnxB,EAGGG,sBACjB,MAjCa,SAsCf6K,OACE,GAAKtN,KAAKwC,SAASlI,YACjB0F,KAAKwC,SAASlI,WAAW9B,WAAaoC,KAAKC,cAC3CmF,KAAKwC,SAAS1H,UAAUC,SA9BJ,WA+BpBJ,EAAWqF,KAAKwC,UAChB,OAGF,IAAI6D,EACJ,MAAMtG,EAAStI,EAAuBuI,KAAKwC,UACrCkxB,EAAc1zB,KAAKwC,SAASY,QAhCN,qBAkC5B,GAAIswB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAYxkB,UAA8C,OAAzBwkB,EAAYxkB,SAjC7C,wBADH,UAmClB7I,EAAWb,EAAeC,KAAKkuB,EAAcD,GAC7CrtB,EAAWA,EAASA,EAAStH,OAAS,GAGxC,MAAM60B,EAAYvtB,EAChBnG,EAAamB,QAAQgF,EArDP,cAqD6B,CACzC0E,cAAe/K,KAAKwC,WAEtB,KAMF,GAJkBtC,EAAamB,QAAQrB,KAAKwC,SAxD5B,cAwDkD,CAChEuI,cAAe1E,IAGH1E,kBAAmC,OAAdiyB,GAAsBA,EAAUjyB,iBACjE,OAGF3B,KAAKizB,UAAUjzB,KAAKwC,SAAUkxB,GAE9B,MAAMnD,EAAW,KACfrwB,EAAamB,QAAQgF,EApEL,gBAoE6B,CAC3C0E,cAAe/K,KAAKwC,WAEtBtC,EAAamB,QAAQrB,KAAKwC,SArEX,eAqEkC,CAC/CuI,cAAe1E,KAIftG,EACFC,KAAKizB,UAAUlzB,EAAQA,EAAOzF,WAAYi2B,GAE1CA,IAMJ0C,UAAUl8B,EAAS0W,EAAWtR,GAC5B,MAIM03B,IAJiBpmB,GAAqC,OAAvBA,EAAUyB,UAA4C,OAAvBzB,EAAUyB,SAE5E1J,EAAeM,SAAS2H,EA5EN,WA2ElBjI,EAAeC,KA1EM,wBA0EmBgI,IAGZ,GACxBS,EAAkB/R,GAAa03B,GAAUA,EAAO/4B,UAAUC,SApF5C,QAsFdw1B,EAAW,IAAMvwB,KAAK8zB,oBAAoB/8B,EAAS88B,EAAQ13B,GAEjE,GAAI03B,GAAU3lB,EAAiB,CAC7B,MAAMvW,EAAqBD,EAAiCm8B,GAC5DA,EAAO/4B,UAAU2C,OAzFC,QA2FlByC,EAAaS,IAAIkzB,EAAQ,gBAAiBtD,GAC1C93B,EAAqBo7B,EAAQl8B,QAE7B44B,IAIJuD,oBAAoB/8B,EAAS88B,EAAQ13B,GACnC,GAAI03B,EAAQ,CACVA,EAAO/4B,UAAU2C,OAtGG,UAwGpB,MAAMs2B,EAAgBvuB,EAAeK,QA9FJ,kCA8F4CguB,EAAOv5B,YAEhFy5B,GACFA,EAAcj5B,UAAU2C,OA3GN,UA8GgB,QAAhCo2B,EAAO58B,aAAa,SACtB48B,EAAOhwB,aAAa,iBAAiB,GAIzC9M,EAAQ+D,UAAUuP,IAnHI,UAoHe,QAAjCtT,EAAQE,aAAa,SACvBF,EAAQ8M,aAAa,iBAAiB,GAGxCpI,EAAO1E,GAEHA,EAAQ+D,UAAUC,SAzHF,SA0HlBhE,EAAQ+D,UAAUuP,IAzHA,QA4HhBtT,EAAQuD,YAAcvD,EAAQuD,WAAWQ,UAAUC,SA/H1B,mBAgIHhE,EAAQqM,QA3HZ,cA8HlBoC,EAAeC,KAzHU,oBA0HtBlM,QAAQy6B,GAAYA,EAASl5B,UAAUuP,IAnIxB,WAsIpBtT,EAAQ8M,aAAa,iBAAiB,IAGpC1H,GACFA,IAMkBwG,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,MAAMC,EAAO3G,EAAKM,IAAI6C,KA7JX,WA6J8B,IAAIyzB,GAAIzzB,MAEjD,GAAsB,iBAAX7G,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,UAYb+G,EAAaQ,GAAG9J,SAxKc,wBAWD,4EA6JyC,SAAUoI,GAC9EA,EAAMqD,kBAEOxF,EAAKM,IAAI6C,KAnLP,WAmL0B,IAAIyzB,GAAIzzB,OAC5CsN,UAUPtR,EA/La,MA+LYy3B,IChMzB,MAeMzsB,GAAc,CAClB0kB,UAAW,UACXuI,SAAU,UACVpI,MAAO,UAGHplB,GAAU,CACdilB,WAAW,EACXuI,UAAU,EACVpI,MAAO,KAWT,MAAMqI,WAAc5xB,EAClBC,YAAYxL,EAASoC,GACnBmO,MAAMvQ,GAENiJ,KAAK+H,QAAU/H,KAAKgI,WAAW7O,GAC/B6G,KAAK6uB,SAAW,KAChB7uB,KAAKivB,gBAKejoB,yBACpB,OAAOA,GAGSP,qBAChB,OAAOA,GAGUhE,sBACjB,MAtDa,WA2Df6K,OAGE,GAFkBpN,EAAamB,QAAQrB,KAAKwC,SAtD5B,iBAwDFb,iBACZ,OAGF3B,KAAKm0B,gBAEDn0B,KAAK+H,QAAQ2jB,WACf1rB,KAAKwC,SAAS1H,UAAUuP,IA5DN,QA+DpB,MAAMkmB,EAAW,KACfvwB,KAAKwC,SAAS1H,UAAU2C,OA7DH,WA8DrBuC,KAAKwC,SAAS1H,UAAUuP,IA/DN,QAiElBnK,EAAamB,QAAQrB,KAAKwC,SArEX,kBAuEXxC,KAAK+H,QAAQksB,WACfj0B,KAAK6uB,SAAW71B,WAAW,KACzBgH,KAAKqN,QACJrN,KAAK+H,QAAQ8jB,SAOpB,GAHA7rB,KAAKwC,SAAS1H,UAAU2C,OA3EJ,QA4EpBhC,EAAOuE,KAAKwC,UACZxC,KAAKwC,SAAS1H,UAAUuP,IA3ED,WA4EnBrK,KAAK+H,QAAQ2jB,UAAW,CAC1B,MAAM/zB,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiB+tB,GACjD93B,EAAqBuH,KAAKwC,SAAU7K,QAEpC44B,IAIJljB,OACE,IAAKrN,KAAKwC,SAAS1H,UAAUC,SAxFT,QAyFlB,OAKF,GAFkBmF,EAAamB,QAAQrB,KAAKwC,SAnG5B,iBAqGFb,iBACZ,OAGF,MAAM4uB,EAAW,KACfvwB,KAAKwC,SAAS1H,UAAUuP,IApGN,QAqGlBnK,EAAamB,QAAQrB,KAAKwC,SA1GV,oBA8GlB,GADAxC,KAAKwC,SAAS1H,UAAU2C,OAvGJ,QAwGhBuC,KAAK+H,QAAQ2jB,UAAW,CAC1B,MAAM/zB,EAAqBD,EAAiCsI,KAAKwC,UAEjEtC,EAAaS,IAAIX,KAAKwC,SAAU,gBAAiB+tB,GACjD93B,EAAqBuH,KAAKwC,SAAU7K,QAEpC44B,IAIJ7tB,UACE1C,KAAKm0B,gBAEDn0B,KAAKwC,SAAS1H,UAAUC,SArHR,SAsHlBiF,KAAKwC,SAAS1H,UAAU2C,OAtHN,QAyHpByC,EAAaC,IAAIH,KAAKwC,SAjIG,0BAmIzB8E,MAAM5E,UACN1C,KAAK+H,QAAU,KAKjBC,WAAW7O,GAST,OARAA,EAAS,IACJsN,MACAtC,EAAYI,kBAAkBvE,KAAKwC,aAChB,iBAAXrJ,GAAuBA,EAASA,EAAS,IAGtDF,EApJS,QAoJaE,EAAQ6G,KAAKuC,YAAYyE,aAExC7N,EAGT81B,gBACE/uB,EAAaQ,GAAGV,KAAKwC,SAtJI,yBAuBC,4BA+HiD,IAAMxC,KAAKqN,QAGxF8mB,gBACEjqB,aAAalK,KAAK6uB,UAClB7uB,KAAK6uB,SAAW,KAKIlsB,uBAACxJ,GACrB,OAAO6G,KAAKuD,MAAK,WACf,IAAIC,EAAO3G,EAAKM,IAAI6C,KArKT,YA4KX,GAJKwD,IACHA,EAAO,IAAI0wB,GAAMl0B,KAHe,iBAAX7G,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqK,EAAKrK,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1CqK,EAAKrK,GAAQ6G,kBAarBhE,EA/La,QA+LYk4B,ICpMV,CACbrxB,MAAAA,EACAc,OAAAA,EACA0D,SAAAA,EACAoF,SAAAA,EACAmY,SAAAA,GACA+B,MAAAA,GACAiD,UAAAA,GACA+H,QAAAA,GACAG,UAAAA,GACA2B,IAAAA,GACAS,MAAAA,GACAvF,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = '#' + hrefAttr.split('#')[1]\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = (name, plugin) => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nexport {\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            // eslint-disable-next-line unicorn/consistent-destructuring\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.0.0-beta3'\n\nclass BaseComponent {\n  constructor(element) {\n    element = typeof element === 'string' ? document.querySelector(element) : element\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    this._element = null\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(element, this.DATA_KEY)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-bs-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASS_NAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASS_NAME_SHOW)\n\n    if (!element.classList.contains(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, 'transitionend', () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.get(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(ORDER_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(ORDER_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n\n    this._items = null\n    this._config = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    if (event.key === ARROW_LEFT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_LEFT)\n    } else if (event.key === ARROW_RIGHT_KEY) {\n      event.preventDefault()\n      this._slide(DIRECTION_RIGHT)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    const isPrev = order === ORDER_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = isPrev ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, 'transitionend', () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_RIGHT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_RIGHT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_NEXT ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_NEXT ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.get(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.get(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${this._element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-bs-target=\"#${this._element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-bs-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Data.get(tempActiveData, DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.set(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, 'transitionend', complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    super.dispose()\n    this._config = null\n    this._parent = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-bs-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.get(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export default function getBoundingClientRect(element) {\n  var rect = element.getBoundingClientRect();\n  return {\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    y: rect.top\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { top, left, right, bottom } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) < 2 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var referenceElement = state.elements.reference;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(referenceElement);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\"; // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement);\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(options) {\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      } else if (typeof this._config.reference === 'object') {\n        referenceElement = this._config.reference\n      }\n\n      const popperConfig = this._getPopperConfig()\n      const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n      this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n      if (isDisplayStatic) {\n        Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n      }\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    this._menu = null\n\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.get(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event) {\n      if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n        return\n      }\n\n      if (/input|select|textarea|form/i.test(event.target.tagName)) {\n        return\n      }\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Data.get(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event) {\n        // Don't close the menu if the clicked element or one of its parents is the dropdown button\n        if ([context._element].some(element => event.composedPath().includes(element))) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu shouldn't close the menu\n        if (event.type === 'keyup' && event.key === TAB_KEY && dropdownMenu.contains(event.target)) {\n          continue\n        }\n      }\n\n      const hideEvent = EventHandler.trigger(toggles[i], EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      Manipulator.removeDataAttribute(dropdownMenu, 'popper')\n      EventHandler.trigger(toggles[i], EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive && (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY)) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.click()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    // Up\n    if (event.key === ARROW_UP_KEY && index > 0) {\n      index--\n    }\n\n    // Down\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) {\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.dropdownInterface(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  isRTL,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, event => this.hide(event))\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    super.dispose()\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._config = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (isAnimated) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, 'transitionend', transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const isAnimated = this._isAnimated()\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (isAnimated) {\n        this._backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (isAnimated) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!isAnimated) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, 'transitionend', callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (isAnimated) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, 'transitionend', callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n    EventHandler.off(this._element, 'transitionend')\n    EventHandler.one(this._element, 'transitionend', () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        EventHandler.one(this._element, 'transitionend', () => {\n          this._element.style.overflowY = ''\n        })\n        emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n    emulateTransitionEnd(this._element, modalTransitionDuration)\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if ((!this._isBodyOverflowing && isModalOverflowing && !isRTL()) || (this._isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if ((this._isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!this._isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n      this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - this._scrollbarWidth)\n      this._setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + this._scrollbarWidth)\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    SelectorEngine.find(selector)\n      .forEach(element => {\n        if (element !== document.body && window.innerWidth > element.clientWidth + this._scrollbarWidth) {\n          return\n        }\n\n        const actualValue = element.style[styleProp]\n        const calculatedValue = window.getComputedStyle(element)[styleProp]\n        Manipulator.setDataAttribute(element, styleProp, actualValue)\n        element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n      })\n  }\n\n  _resetScrollbar() {\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n    this._resetElementAttributes('body', 'paddingRight')\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    SelectorEngine.find(selector).forEach(element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined' && element === document.body) {\n        element.style[styleProp] = ''\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    })\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.get(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.toggle(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst getWidth = () => {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = document.documentElement.clientWidth\n  return Math.abs(window.innerWidth - documentWidth)\n}\n\nconst hide = (width = getWidth()) => {\n  document.body.style.overflow = 'hidden'\n  _setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n  _setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  _setElementAttributes('body', 'paddingRight', calculatedValue => calculatedValue + width)\n}\n\nconst _setElementAttributes = (selector, styleProp, callback) => {\n  const scrollbarWidth = getWidth()\n  SelectorEngine.find(selector)\n    .forEach(element => {\n      if (element !== document.body && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      const actualValue = element.style[styleProp]\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n      element.style[styleProp] = callback(Number.parseFloat(calculatedValue)) + 'px'\n    })\n}\n\nconst reset = () => {\n  document.body.style.overflow = 'auto'\n  _resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n  _resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  _resetElementAttributes('body', 'paddingRight')\n}\n\nconst _resetElementAttributes = (selector, styleProp) => {\n  SelectorEngine.find(selector).forEach(element => {\n    const value = Manipulator.getDataAttribute(element, styleProp)\n    if (typeof value === 'undefined' && element === document.body) {\n      element.style.removeProperty(styleProp)\n    } else {\n      Manipulator.removeDataAttribute(element, styleProp)\n      element.style[styleProp] = value\n    }\n  })\n}\n\nconst isBodyOverflowing = () => {\n  return getWidth() > 0\n}\n\nexport {\n  getWidth,\n  hide,\n  isBodyOverflowing,\n  reset\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport { hide as scrollBarHide, reset as scrollBarReset } from './util/scrollbar'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_BACKDROP_BODY = 'offcanvas-backdrop'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_TOGGLING = 'offcanvas-toggling'\nconst OPEN_SELECTOR = '.offcanvas.show'\nconst ACTIVE_SELECTOR = `${OPEN_SELECTOR}, .${CLASS_NAME_TOGGLING}`\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"offcanvas\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    if (this._config.backdrop) {\n      document.body.classList.add(CLASS_NAME_BACKDROP_BODY)\n    }\n\n    if (!this._config.scroll) {\n      scrollBarHide()\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n      this._enforceFocusOnElement(this._element)\n    }\n\n    setTimeout(completeCallBack, getTransitionDurationFromElement(this._element))\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.add(CLASS_NAME_TOGGLING)\n    EventHandler.off(document, EVENT_FOCUSIN)\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (this._config.backdrop) {\n        document.body.classList.remove(CLASS_NAME_BACKDROP_BODY)\n      }\n\n      if (!this._config.scroll) {\n        scrollBarReset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n      this._element.classList.remove(CLASS_NAME_TOGGLING)\n    }\n\n    setTimeout(completeCallback, getTransitionDurationFromElement(this._element))\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _enforceFocusOnElement(element) {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n        element !== event.target &&\n        !element.contains(event.target)) {\n        element.focus()\n      }\n    })\n    element.focus()\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n\n    EventHandler.on(document, 'keydown', event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n\n    EventHandler.on(document, EVENT_CLICK_DATA_API, event => {\n      const target = SelectorEngine.findOne(getSelectorFromElement(event.target))\n      if (!this._element.contains(event.target) && target !== this._element) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Offcanvas(this, typeof config === 'object' ? config : {})\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(ACTIVE_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    return\n  }\n\n  const data = Data.get(target, DATA_KEY) || new Offcanvas(target)\n\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => (Data.get(el, DATA_KEY) || new Offcanvas(el)).show())\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(NAME, Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n    EventHandler.off(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip && this.tip.parentNode) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.config = null\n    this.tip = null\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    this.setContent()\n\n    if (this.config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this.config.placement === 'function' ?\n      this.config.placement.call(this, tip, this._element) :\n      this.config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const container = this._getContainer()\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.appendChild(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = typeof this.config.customClass === 'function' ? this.config.customClass() : this.config.customClass\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop())\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(this.tip)\n      EventHandler.one(this.tip, 'transitionend', complete)\n      emulateTransitionEnd(this.tip, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      if (this._popper) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, 'transitionend', complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this._element.getAttribute('data-bs-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this._element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.get(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(event.delegateTarget, this._getDelegateConfig())\n      Data.set(event.delegateTarget, dataKey, context)\n    }\n\n    return context\n  }\n\n  _getOffset() {\n    const { offset } = this.config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            altBoundary: true,\n            fallbackPlacements: this.config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this.config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this.config.popperConfig === 'function' ? this.config.popperConfig(defaultBsPopperConfig) : this.config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this.config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this.config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this._element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getContent() {\n    return this._element.getAttribute('data-bs-content') || this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.set(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    super.dispose()\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isDisabled,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      isDisabled(this._element)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, 'transitionend', complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.get(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.get(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-bs-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, 'transitionend', complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n\n    super.dispose()\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.get(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(NAME, Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-beta3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  <PERSON><PERSON>,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}