<?php
// Step 0: Set credentials and config
$clientId = '3_4eqwsqat994w88ss848wssocg0g88sgkc00c8s8884sow04kcc';
$clientSecret = '4e1l6l603h2ckscg0c00gg00o4kkgsocgo0cc8woowk4sgsw80';
$mauticBaseUrl = 'https://crm.goglobalconference.com';
$segmentId = '100';
$segmentId = '98';

// Step 1: Get Access Token
$tokenUrl = $mauticBaseUrl . '/oauth/v2/token';
$postFields = [
    'grant_type' => 'client_credentials',
    'client_id' => $clientId,
    'client_secret' => $clientSecret,
];

$ch = curl_init($tokenUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postFields));
$tokenResponse = curl_exec($ch);
curl_close($ch);

$tokenData = json_decode($tokenResponse, true);
$accessToken = $tokenData['access_token'];

/* $_SESSION['ggc_email'] = '<EMAIL>';
$first_name = 'leo';
$last_name = 'lastleo';
$contact_phone = '1234567890';
$company_name = 'cm leo';
$city_state_zip = 'cszleo'; */


// Get the current user ID from session
$user_id = $_SESSION['ggc_user_id'];
//$user_id = '20250703021932_59876f2d';

if (!empty($user_id)) {

    global $wpdb;

    $table_registration = 'user_registration';
    $table_payments = 'user_payments';

    // If user ID is present, run the query
    if (!empty($user_id)) {
        $query = $wpdb->prepare("
            SELECT ur.*, up.*
            FROM $table_registration ur
            LEFT JOIN $table_payments up ON ur.client_id = up.client_id
            WHERE ur.client_id = %s
        ", $user_id);

        $result = $wpdb->get_row($query); // returns single object row

    } else {
        //die('User ID not found.');
    }

    if ($result) {
        $contactData = [
            'email' => $result->email,
            'firstname' => $result->first_name,
            'lastname' => $result->last_name,
            'phone' => $result->contact_phone,
            'company' => $result->company_name,
            'address1' => $result->city_state_zip
        ];
    } else {
        //die('No payment record found for this user.');
    }
} else {
    die('No user found.');
}

try {

    // Step 3: Create/Update contact
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . '/api/contacts/new');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($contactData));
    $contactResponse = curl_exec($ch);
    $contactData = json_decode($contactResponse, true);

    curl_close($ch);

    $contactId = $contactData['contact']['id'];

    $post_uri_segment = "/api/segments/$segmentId/contact/$contactId/add";

    // Step 4: Add contact to segment
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . $post_uri_segment);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    $segmentResponse = curl_exec($ch);
    $segmentResponse = json_decode($segmentResponse, true);

    curl_close($ch);

    global $wpdb;

    // Define your table name with prefix
    $table_name = 'user_mautic';

    // Prepare data
    $data = array(
        'client_id'     => $user_id,
        'contact_id'    => $contactId,
        'data_user'     => json_encode($contactData), // or your actual data
        'data_segment' => json_encode($segmentResponse), // or your actual data
        'time_sent'     => date('F j, Y g:i A') // WordPress current time in MySQL DATETIME format
    );

    $format = array(
        '%s',  // data_send
        '%s',  // data_send
        '%s',  // data_send
        '%d',  // data_response
        '%s'   // time_sent
    );

    // Insert into the table
    $wpdb->insert($table_name, $data, $format);
} catch (Exception $e) {
    //die('Failed to create contact.');
}
