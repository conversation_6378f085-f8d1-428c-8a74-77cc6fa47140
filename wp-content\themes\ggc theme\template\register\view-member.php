<?php
// Check if user is logged in
if (empty($_SESSION['ggc_login'])) {
    wp_redirect(home_url('/ggc-login/'));
    exit;
}

$id = $_GET['id'];
if (empty($id)) {
    $id = $_SESSION['ggc_user_id'];
}

global $wpdb;
$table_name = 'user_registration';
$payments_table = 'user_payments';

// Join user_registration with user_payments using client_id
$query = $wpdb->prepare("
    SELECT ur.*, up.*
    FROM $table_name ur
    LEFT JOIN $payments_table up ON ur.client_id = up.client_id AND ur.client_id IS NOT NULL AND ur.client_id != ''
    WHERE ur.client_id = %s
", $id);
$row = $wpdb->get_row($query);
?>

<!-- leads area -->
<div class="about-area py-2 px-2">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <?php
                if ($row) :
                ?>
                    <div class="site-heading mb-5">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="site-title-tagline">Conference Profile</span>
                                <h2 class="site-title">
                                    Welcome <span><?php echo esc_html($row->first_name); ?> <?php echo esc_html($row->last_name); ?></span>
                                </h2>
                            </div>
                            <?php if ($_SESSION['ggc_admin']) : ?>
                                <div>
                                    <a href="/ggc-register-members" class="theme-btn"><i class="far fa-arrow-left"></i> Back to Members</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="profile-heading team-session pb-50">
                        <div class="container-fluid">
                            <div class="row g-4 wow fadeInUp" data-wow-delay=".25s" style="visibility: visible; animation-delay: 0.25s; animation-name: fadeInUp;">
                                <div class="col-lg-4">
                                    <div class="session-item">
                                        <h6 class="day">Company Information</h6>

                                        <p><strong>Name:</strong> <?php echo esc_html($row->company_name); ?></p>

                                        <p><strong>Phone:</strong> <?php echo esc_html($row->contact_phone); ?></p>

                                        <p><strong>Email:</strong> <?php echo esc_html($row->email); ?></p>

                                        <p><strong>Website:</strong> <a href="<?php echo esc_url($row->company_website); ?>" target="_blank"><?php echo esc_html($row->company_website); ?></a></p>


                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="session-item">
                                        <h6 class="day">Payment Information</h6>
                                        <p><strong>Payment Name:</strong>
                                            <?php if (!empty($row->pay_name)) : ?>
                                                <?php echo esc_html($row->pay_name); ?>
                                            <?php else : ?>
                                                No name recorded
                                            <?php endif; ?>
                                        </p>
                                        <p><strong>Payment Email:</strong>
                                            <?php if (!empty($row->pay_email)) : ?>
                                                <?php echo esc_html($row->pay_email); ?>
                                            <?php else : ?>
                                                No email recorded
                                            <?php endif; ?>
                                        </p>
                                        <p><strong>Payment Phone:</strong>
                                            <?php if (!empty($row->pay_phone)) : ?>
                                                <?php echo esc_html($row->pay_phone); ?>
                                            <?php else : ?>
                                                No name recorded
                                            <?php endif; ?>
                                        </p>
                                        <p><strong>Type:</strong>
                                            <?php if (!empty($row->product_name)) : ?>
                                                <?php echo esc_html($row->product_name); ?>
                                            <?php else : ?>
                                                No type recorded
                                            <?php endif; ?>
                                        </p>
                                        <p><strong>Payment Amount:</strong>
                                            <?php if (!empty($row->amount)) : ?>
                                                <?php echo esc_html($row->amount); ?> USD
                                            <?php else : ?>
                                                No payment recorded
                                            <?php endif; ?>
                                        </p>
                                        <p><strong>Date Paid:</strong>
                                            <?php if (!empty($row->created_at)) : ?>
                                                <?php echo date('M j, Y g:i A', $row->created_at); ?>
                                            <?php else : ?>
                                                No payment date
                                            <?php endif; ?>
                                        </p>

                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="session-item">
                                        <h6 class="day">Business Profile</h6>
                                        <p><strong>Status:</strong> Completed</p>
                                        <p><a href="/ggc-update-business-profile/" class="update-proflie theme-btn">Update Profile<i class="far fa-arrow-right"></i></a></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="registration-details wow fadeInUp" data-wow-delay=".25s">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="site-heading text-center wow fadeInDown" data-wow-delay=".25s" style="visibility: visible; animation-delay: 0.25s; animation-name: fadeInDown;">
                                        <h2 class="site-title">Business Profile</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <h3>Uploads</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-md-4">
                                            <p><strong>Company Logo:</strong></p>
                                            <?php if ($row->company_logo_upload) : ?>
                                                <div class="image-preview-container">
                                                    <a href="<?php echo esc_url($row->company_logo_upload); ?>" class="popup-img">
                                                        <img src="<?php echo esc_url($row->company_logo_upload); ?>" alt="Company Logo" class="img-fluid clickable-image" style="max-height: 250px; cursor: pointer;">
                                                        <div class="image-overlay">
                                                            <i class="fal fa-search-plus"></i>
                                                        </div>
                                                    </a>
                                                </div>
                                            <?php else : ?>
                                                <p>Not uploaded</p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-4">
                                            <p><strong>Representative Headshot:</strong></p>
                                            <?php if ($row->headshot_upload) : ?>
                                                <div class="image-preview-container">
                                                    <a href="<?php echo esc_url($row->headshot_upload); ?>" class="popup-img">
                                                        <img src="<?php echo esc_url($row->headshot_upload); ?>" alt="Representative Headshot" class="img-fluid clickable-image" style="max-height: 250px; cursor: pointer;">
                                                        <div class="image-overlay">
                                                            <i class="fal fa-search-plus"></i>
                                                        </div>
                                                    </a>
                                                </div>
                                            <?php else : ?>
                                                <p>Not uploaded</p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-4">
                                            <p><strong>Business Profile:</strong></p>
                                            <?php if ($row->business_profile_upload) : ?>
                                                <a href="<?php echo esc_url($row->business_profile_upload); ?>" target="_blank" class="theme-btn">View Business Profile</a>
                                            <?php else : ?>
                                                <p>Not uploaded</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <h3>Personal Information</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-md-6">
                                            <p><strong>First Name:</strong> <?php echo esc_html($row->first_name); ?></p>
                                            <p><strong>Last Name:</strong> <?php echo esc_html($row->last_name); ?></p>
                                            <p><strong>Phone:</strong> <?php echo esc_html($row->contact_phone); ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Country:</strong> <?php echo esc_html($row->country); ?></p>
                                            <p><strong>City/State/ZIP:</strong> <?php echo esc_html($row->city_state_zip); ?></p>
                                            <p><strong>Preferred Language:</strong> <?php echo esc_html($row->preferred_language); ?></p>
                                        </div>
                                    </div>

                                    <h3>Business Information</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-md-6">
                                            <p><strong>Company Name:</strong> <?php echo esc_html($row->company_name); ?></p>
                                            <p><strong>Company Website:</strong> <a href="<?php echo esc_url($row->company_website); ?>" target="_blank"><?php echo esc_html($row->company_website); ?></a></p>
                                            <p><strong>LinkedIn:</strong> <a href="<?php echo esc_url($row->linkedin_url); ?>" target="_blank"><?php echo esc_html($row->linkedin_url); ?></a></p>
                                            <p><strong>Other Social Media:</strong> <a href="<?php echo esc_html($row->social_media_links); ?>" target="_blank"><?php echo esc_html($row->social_media_links); ?></a></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Years in Operation:</strong> <?php echo esc_html($row->years_in_operation); ?></p>
                                            <p><strong>Number of Employees:</strong> <?php echo esc_html($row->number_of_employees); ?></p>
                                            <p><strong>Annual Revenue:</strong> <?php echo esc_html($row->annual_revenue); ?></p>
                                        </div>
                                        <div class="col-12">
                                            <p><strong>Business Description:</strong></p>
                                            <p><?php echo esc_html($row->business_description); ?></p>
                                        </div>
                                    </div>

                                    <h3>Representative Attending</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-md-6">
                                            <p><strong>Name:</strong> <?php echo esc_html($row->rep_name); ?></p>
                                            <p><strong>Title:</strong> <?php echo esc_html($row->rep_title); ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Email:</strong> <?php echo esc_html($row->rep_email); ?></p>
                                            <p><strong>Phone:</strong> <?php echo esc_html($row->rep_phone); ?></p>
                                        </div>
                                    </div>

                                    <h3>Program Participation</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <p><strong>Attending Days:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php if ($row->attending_day1) : ?>
                                                    <li>Day 1 - Welcome Reception (6:00 PM - 10:00 PM)</li>
                                                <?php endif; ?>
                                                <?php if ($row->attending_day2) : ?>
                                                    <li>Day 2 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)</li>
                                                <?php endif; ?>
                                                <?php if ($row->attending_day3) : ?>
                                                    <li>Day 3 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)</li>
                                                <?php endif; ?>
                                                <?php if ($row->attending_day4_visits) : ?>
                                                    <li>Day 4 - Site Visits (International Attendees ONLY)</li>
                                                <?php endif; ?>
                                                <?php if ($row->attending_day4_closing) : ?>
                                                    <li>Day 4 - Closing Reception (5:00 PM - 9:00 PM)</li>
                                                <?php endif; ?>
                                            </ul>
                                            <p><strong>Primary objectives for attending:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $importing_regions = explode(',', $row->primary_objectives);
                                                foreach ($importing_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>Will be attending as a:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $importing_regions = explode(',', $row->attendee_type);
                                                foreach ($importing_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>Need translation or interpretation support:</strong></p>
                                            <p><?php echo $row->need_translation ? 'Yes' : 'No'; ?> / <?php echo esc_html($row->translation_language); ?></p>
                                        </div>
                                    </div>

                                    <h3>Trade Activity</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <p><strong>Importing products or services (From Where?):</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $importing_regions = explode(',', $row->importing_regions);
                                                foreach ($importing_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>Exporting products or services (To Where?):</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $exporting_regions = explode(',', $row->exporting_regions);
                                                foreach ($exporting_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>

                                            <p><strong>Geographic areas in which you are serving with your products/services:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $service_areas = explode(',', $row->service_areas);
                                                foreach ($service_areas as $area) :
                                                    if (trim($area)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($area)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>Exhibiting or part of a country/company pavilion:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $service_areas = explode(',', $row->exhibiting_option);
                                                foreach ($service_areas as $area) :
                                                    if (trim($area)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($area)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                        </div>
                                    </div>

                                    <h3>Company Profile and Objectives</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <p><strong>What you are looking for in this B2B Matchmaking event at the Go Global Conference:</strong></p>
                                            <p><?php echo esc_html($row->matchmaking_objectives); ?></p>
                                            <p><strong>Which industry/sector best describes your business:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $service_areas = explode(',', $row->industry_sectors);
                                                foreach ($service_areas as $area) :
                                                    if (trim($area)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($area)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>Company Type:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $service_areas = explode(',', $row->company_type);
                                                foreach ($service_areas as $area) :
                                                    if (trim($area)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($area)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>Business offering to potential partners:</strong></p>
                                            <p><?php echo esc_html($row->business_offering); ?></p>
                                            <p><strong>Business seeking from potential partners:</strong></p>
                                            <p><?php echo esc_html($row->business_seeking); ?></p>
                                            <p><strong>Specific international suppliers, products, services, partnerships, or investment opportunities you're seeking from your match:</strong></p>
                                            <p><?php echo esc_html($row->specific_needs); ?></p>
                                            <p><strong>Geographic areas or markets are you interested in expanding into:</strong> </p>
                                            <p><?php echo esc_html($row->expansion_markets); ?> / <?php echo esc_html($row->expansion_region); ?></p>
                                            <p><strong>How would this program benefit you or your company:</strong></p>
                                            <p><?php echo esc_html($row->program_benefits); ?></p>
                                            <p><strong>What Type of companies do you want to meet:</strong></p>
                                            <p><?php echo esc_html($row->companies_to_meet); ?></p>
                                            <p><strong>Preferred meeting time during the event:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $exporting_regions = explode(',', $row->preferred_meeting_time);
                                                foreach ($exporting_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                        </div>
                                    </div>

                                    <h3>Previous Participation & Referrals</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <p><strong>Have you participated in a B2B Matchmaking meeting before:</strong></p>
                                            <p><?php echo $row->participated_before ? 'Yes' : 'No'; ?></p>
                                            <p><strong>Have you attended similar programs before:</strong></p>
                                            <p><?php echo esc_html($row->previous_programs); ?></p>
                                            <p><strong>organization(s) to which you belong:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $exporting_regions = explode(',', $row->memberships);
                                                foreach ($exporting_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>How did you hear about this opportunity:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php
                                                $exporting_regions = explode(',', $row->referral_sources);
                                                foreach ($exporting_regions as $region) :
                                                    if (trim($region)) :
                                                ?>
                                                        <li><?php echo esc_html(trim($region)); ?></li>
                                                <?php
                                                    endif;
                                                endforeach;
                                                ?>
                                            </ul>
                                            <p><strong>What does success at this year's event look like for you:</strong></p>
                                            <p><?php echo esc_html($row->success_description); ?></p>
                                            <p><strong>Additional questions or comments:</strong></p>
                                            <p><?php echo esc_html($row->questions_comments); ?></p>
                                        </div>
                                    </div>

                                    <h3>Consent</h3>
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <p><strong>Would you like your company to be highlighted in the event materials or media:</strong> <?php echo esc_html($row->highlight_company); ?></p>
                                            <p><strong>Consent to store data:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php if ($row->consent_store_data) : ?>
                                                    <li>I Consent to store data and receive updates</li>
                                                <?php endif; ?>
                                            </ul>
                                            <p><strong>Confirm Participation:</strong></p>
                                            <ul class="list-unstyled">
                                                <?php if ($row->confirm_participation) : ?>
                                                    <li>I agree to formally confirm my participation in the selected program(s)</li>
                                                <?php endif; ?>
                                                <?php if ($row->share_with_partners) : ?>
                                                    <li>I agree to have my information shared with potential matchmaking partners.</li>
                                                <?php endif; ?>
                                                <?php if ($row->post_event_contact) : ?>
                                                    <li>I consent to be contacted after the event for feedback and future opportunities.</li>
                                                <?php endif; ?>
                                                <?php if ($row->agree_terms) : ?>
                                                    <li>I agree to the terms and conditions / code of conduct (on goglobalconference.com) of this event.</li>
                                                <?php endif; ?>
                                                <?php if ($row->disagree_terms) : ?>
                                                    <li>I do not agree.</li>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php else : ?>
                    <?php get_template_part('template/register/error'); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<!-- leads area end -->

<script>
    jQuery(document).ready(function($) {
        // Initialize Magnific Popup for the upload images
        $('.image-preview-container .popup-img').magnificPopup({
            type: 'image',
            closeOnContentClick: true,
            mainClass: 'mfp-img-mobile',
            image: {
                verticalFit: true
            }
        });
    });
</script>