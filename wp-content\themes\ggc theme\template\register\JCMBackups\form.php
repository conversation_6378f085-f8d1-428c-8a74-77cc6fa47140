<?php
// Generate a unique ID for the client reference
if (!empty($_SESSION['user_id'])) {
    $unique_id = $_SESSION['user_id'];
} else {
    wp_redirect(home_url('/ggc-register'));
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && check_admin_referer('register_user_nonce')) {
    // Database connection using WordPress globals
    global $wpdb;
    $table_name = 'user_registration';

    try {
        // Handle file uploads
        $upload_dir = wp_upload_dir();
        $business_profile = '';
        $company_logo = '';
        $headshot = '';
        $max_size = 5 * 1024 * 1024; // 5MB

        // Validate required files
        if (empty($_FILES['business_profile']['name']) || empty($_FILES['company_logo']['name']) || empty($_FILES['headshot']['name'])) {
            throw new Exception('All files (Business Profile, Company Logo, and Headshot) are required.');
        }

        // Handle business profile upload
        if (!empty($_FILES['business_profile']['name'])) {
            $business_profile_file = $_FILES['business_profile'];

            // Validate file size
            if ($business_profile_file['size'] > $max_size) {
                throw new Exception('Business Profile file size must not exceed 5MB.');
            }

            // Validate file type
            $file_type = strtolower(pathinfo($business_profile_file['name'], PATHINFO_EXTENSION));
            if ($file_type !== 'pdf') {
                throw new Exception('Business Profile must be a PDF file.');
            }

            $business_profile_path = $upload_dir['path'] . '/' . sanitize_file_name($business_profile_file['name']);
            if (move_uploaded_file($business_profile_file['tmp_name'], $business_profile_path)) {
                $business_profile = $upload_dir['url'] . '/' . sanitize_file_name($business_profile_file['name']);
            } else {
                throw new Exception('Failed to upload Business Profile.');
            }
        }

        // Handle company logo upload
        if (!empty($_FILES['company_logo']['name'])) {
            $company_logo_file = $_FILES['company_logo'];

            // Validate file size
            if ($company_logo_file['size'] > $max_size) {
                throw new Exception('Company Logo file size must not exceed 5MB.');
            }

            // Validate file type
            $file_type = strtolower(pathinfo($company_logo_file['name'], PATHINFO_EXTENSION));
            if (!in_array($file_type, ['jpg', 'jpeg', 'png'])) {
                throw new Exception('Company Logo must be a JPG or PNG file.');
            }

            $company_logo_path = $upload_dir['path'] . '/' . sanitize_file_name($company_logo_file['name']);
            if (move_uploaded_file($company_logo_file['tmp_name'], $company_logo_path)) {
                $company_logo = $upload_dir['url'] . '/' . sanitize_file_name($company_logo_file['name']);
            } else {
                throw new Exception('Failed to upload Company Logo.');
            }
        }

        // Handle headshot upload
        if (!empty($_FILES['headshot']['name'])) {
            $headshot_file = $_FILES['headshot'];

            // Validate file size
            if ($headshot_file['size'] > $max_size) {
                throw new Exception('Headshot file size must not exceed 5MB.');
            }

            // Validate file type
            $file_type = strtolower(pathinfo($headshot_file['name'], PATHINFO_EXTENSION));
            if (!in_array($file_type, ['jpg', 'jpeg', 'png'])) {
                throw new Exception('Headshot must be a JPG or PNG file.');
            }

            $headshot_path = $upload_dir['path'] . '/' . sanitize_file_name($headshot_file['name']);
            if (move_uploaded_file($headshot_file['tmp_name'], $headshot_path)) {
                $headshot = $upload_dir['url'] . '/' . sanitize_file_name($headshot_file['name']);
            } else {
                throw new Exception('Failed to upload Headshot.');
            }
        }
        // Sanitize and collect form data
        $participated_before = isset($_POST['b2bMatchParticipation']) ? ($_POST['b2bMatchParticipation'] === 'Yes' ? 1 : 0) : null;
        $first_name = sanitize_text_field($_POST['firstName']);
        $last_name = sanitize_text_field($_POST['lastName']);
        $contact_phone = sanitize_text_field($_POST['phoneNumber']);
        $preferred_language = sanitize_text_field($_POST['preferredLanguage']);
        $country = sanitize_text_field($_POST['country']);
        $city_state_zip = sanitize_text_field($_POST['address']);
        $company_name = sanitize_text_field($_POST['companyname']);
        $company_website = esc_url_raw($_POST['companyWebsite']);
        $linkedin_url = esc_url_raw($_POST['linkedIn']);
        $social_media_links = sanitize_text_field($_POST['socialMedia']);
        $business_description = sanitize_textarea_field($_POST['describeBusiness']);
        $years_in_operation = absint($_POST['yearsBusiness']);
        $number_of_employees = absint($_POST['numEmployees']);
        $annual_revenue = sanitize_text_field($_POST['annualRevenue']);
        $rep_name = sanitize_text_field($_POST['repName']);
        $rep_title = sanitize_text_field($_POST['repTitle']);
        $rep_email = sanitize_email($_POST['repEmail']);
        $rep_phone = sanitize_text_field($_POST['repPhone']);

        // Handle conference days
        $attending_days = isset($_POST['conference-days-participating']) ? $_POST['conference-days-participating'] : [];
        $attending_day1 = in_array('Day 1 - Welcome Reception (6:00 PM - 10:00 PM)', $attending_days) ? 1 : 0;
        $attending_day2 = in_array('Day 2 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)', $attending_days) ? 1 : 0;
        $attending_day3 = in_array('Day 3 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)', $attending_days) ? 1 : 0;
        $attending_day4_visits = in_array('Day 4 - Site Visits (International Attendees ONLY)', $attending_days) ? 1 : 0;
        $attending_day4_closing = in_array('Day 4 - Closing Reception (5:00 PM - 9:00 PM)', $attending_days) ? 1 : 0;

        // Sanitize and collect additional form data
        $primary_objectives = isset($_POST['primary-objectives']) ? sanitize_text_field(implode(',', $_POST['primary-objectives'])) : null;
        $attendee_type = sanitize_text_field($_POST['attendee_type']);
        $need_translation = isset($_POST['need_translation']) ? 1 : 0;
        $translation_language = sanitize_text_field($_POST['translation_language']);
        $importing_regions = isset($_POST['importing_regions']) ? sanitize_text_field(implode(',', $_POST['importing_regions'])) : null;
        $exporting_regions = isset($_POST['exporting_regions']) ? sanitize_text_field(implode(',', $_POST['exporting_regions'])) : null;
        $service_areas = sanitize_text_field($_POST['service_areas']);
        $exhibiting_option = sanitize_text_field($_POST['exhibiting_option']);
        $matchmaking_objectives = sanitize_textarea_field($_POST['matchmaking_objectives']);
        $industry_sectors = isset($_POST['industry_sectors']) ? sanitize_text_field(implode(',', $_POST['industry_sectors'])) : null;
        $company_type = isset($_POST['company_type']) ? sanitize_text_field(implode(',', $_POST['company_type'])) : null;
        $business_offering = sanitize_textarea_field($_POST['business_offering']);
        $business_seeking = sanitize_textarea_field($_POST['business_seeking']);
        $specific_needs = sanitize_textarea_field($_POST['specific_needs']);
        $expansion_markets = isset($_POST['expansion_markets']) ? sanitize_text_field(implode(',', $_POST['expansion_markets'])) : null;
        $expansion_region = sanitize_text_field($_POST['expansion_region']);
        $program_benefits = sanitize_textarea_field($_POST['program_benefits']);
        $companies_to_meet = sanitize_textarea_field($_POST['companies_to_meet']);
        $preferred_meeting_time = isset($_POST['preferred_meeting_time']) ? sanitize_text_field(implode(',', $_POST['preferred_meeting_time'])) : null;
        $previous_programs = sanitize_textarea_field($_POST['previous_programs']);
        $memberships = isset($_POST['memberships']) ? sanitize_text_field(implode(',', $_POST['memberships'])) : null;
        $referral_sources = isset($_POST['referral_sources']) ? sanitize_text_field(implode(',', $_POST['referral_sources'])) : null;
        $success_description = sanitize_textarea_field($_POST['success_description']);
        $questions_comments = sanitize_textarea_field($_POST['questions_comments']);
        $highlight_company = sanitize_text_field($_POST['highlight_company']);
        $disagree_terms = isset($_POST['disagree_terms']) ? 1 : 0;

        // Generate username and password
        $username = strtolower(substr($first_name, 0, 1) . $last_name);
        $base_username = $username;
        $counter = 1;

        // Check if username exists and generate a unique one
        while ($wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE username = %s", $username))) {
            $username = $base_username . $counter;
            $counter++;
        }

        $password = wp_hash_password(wp_generate_password(12, true, true));

        // Prepare data for insertion
        $data = array(
            'username' => $username,
            'client_id' => $unique_id,
            'password' => $password,
            'participated_before' => $participated_before,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'contact_phone' => $contact_phone,
            'preferred_language' => $preferred_language,
            'country' => $country,
            'city_state_zip' => $city_state_zip,
            'company_name' => $company_name,
            'company_website' => $company_website,
            'linkedin_url' => $linkedin_url,
            'social_media_links' => $social_media_links,
            'business_description' => $business_description,
            'years_in_operation' => $years_in_operation,
            'number_of_employees' => $number_of_employees,
            'annual_revenue' => $annual_revenue,
            'rep_name' => $rep_name,
            'rep_title' => $rep_title,
            'rep_email' => $rep_email,
            'rep_phone' => $rep_phone,
            'attending_day1' => $attending_day1,
            'attending_day2' => $attending_day2,
            'attending_day3' => $attending_day3,
            'attending_day4_visits' => $attending_day4_visits,
            'attending_day4_closing' => $attending_day4_closing,
            'primary_objectives' => $primary_objectives,
            'attendee_type' => $attendee_type,
            'need_translation' => $need_translation,
            'translation_language' => $translation_language,
            'importing_regions' => $importing_regions,
            'exporting_regions' => $exporting_regions,
            'service_areas' => $service_areas,
            'exhibiting_option' => $exhibiting_option,
            'matchmaking_objectives' => $matchmaking_objectives,
            'industry_sectors' => $industry_sectors,
            'company_type' => $company_type,
            'business_offering' => $business_offering,
            'business_seeking' => $business_seeking,
            'specific_needs' => $specific_needs,
            'expansion_markets' => $expansion_markets,
            'expansion_region' => $expansion_region,
            'program_benefits' => $program_benefits,
            'companies_to_meet' => $companies_to_meet,
            'preferred_meeting_time' => $preferred_meeting_time,
            'previous_programs' => $previous_programs,
            'memberships' => $memberships,
            'referral_sources' => $referral_sources,
            'success_description' => $success_description,
            'questions_comments' => $questions_comments,
            'highlight_company' => $highlight_company,
            'business_profile_upload' => $business_profile,
            'company_logo_upload' => $company_logo,
            'headshot_upload' => $headshot,
            'consent_store_data' => 1,
            'confirm_participation' => 1,
            'share_with_partners' => 1,
            'post_event_contact' => 1,
            'agree_terms' => 1,
            'disagree_terms' => $disagree_terms
        );

        $format = array(
            '%s', // username
            '%s', // client_id
            '%s', // password
            '%d', // participated_before
            '%s', // first_name
            '%s', // last_name
            '%s', // contact_phone
            '%s', // preferred_language
            '%s', // country
            '%s', // city_state_zip
            '%s', // company_name
            '%s', // company_website
            '%s', // linkedin_url
            '%s', // social_media_links
            '%s', // business_description
            '%d', // years_in_operation
            '%d', // number_of_employees
            '%s', // annual_revenue
            '%s', // rep_name
            '%s', // rep_title
            '%s', // rep_email
            '%s', // rep_phone
            '%d', // attending_day1
            '%d', // attending_day2
            '%d', // attending_day3
            '%d', // attending_day4_visits
            '%d', // attending_day4_closing
            '%s', // primary_objectives
            '%s', // attendee_type
            '%d', // need_translation
            '%s', // translation_language
            '%s', // importing_regions
            '%s', // exporting_regions
            '%s', // service_areas
            '%s', // exhibiting_option
            '%s', // matchmaking_objectives
            '%s', // industry_sectors
            '%s', // company_type
            '%s', // business_offering
            '%s', // business_seeking
            '%s', // specific_needs
            '%s', // expansion_markets
            '%s', // expansion_region
            '%s', // program_benefits
            '%s', // companies_to_meet
            '%s', // preferred_meeting_time
            '%s', // previous_programs
            '%s', // memberships
            '%s', // referral_sources
            '%s', // success_description
            '%s', // questions_comments
            '%s', // highlight_company
            '%s', // business_profile_upload
            '%s', // company_logo_upload
            '%s', // headshot_upload
            '%d', // consent_store_data
            '%d', // confirm_participation
            '%d', // share_with_partners
            '%d', // post_event_contact
            '%d', // agree_terms
            '%d'  // disagree_terms
        );

        // Insert the data
        $result = $wpdb->insert($table_name, $data, $format);

        if ($result !== false) {
            wp_redirect(home_url('/ggc-thank-you'));
            exit;
        } else {
            echo "<div class='alert alert-danger'>Error: Registration failed. Please try again.</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . esc_html($e->getMessage()) . "</div>";
    }
}
?>

<?php get_template_part('template/register/form_data'); ?>

<script>
    (() => {
        'use strict';
        const form = document.getElementById('dayForm');

        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        });
    })();
</script>
<script>
    // Add file size validation
    document.querySelectorAll('input[type="file"]').forEach(function(input) {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const maxSize = 5 * 1024 * 1024; // 5MB

            if (file && file.size > maxSize) {
                alert('File size must not exceed 5MB');
                e.target.value = '';
            }

            // Validate file type
            if (file) {
                const acceptedTypes = e.target.accept.split(',');
                const fileType = '.' + file.name.split('.').pop().toLowerCase();
                if (!acceptedTypes.includes(fileType)) {
                    alert('Invalid file type. Please upload a file with the correct format.');
                    e.target.value = '';
                }
            }
        });
    });
</script>
<script>
    // Example starter JavaScript for disabling form submissions if there are invalid fields
    (function() {
        'use strict'

        // Fetch all the forms we want to apply custom Bootstrap validation styles to
        var forms = document.querySelectorAll('.needs-validation')

        // Loop over them and prevent submission
        Array.prototype.slice.call(forms)
            .forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }

                    form.classList.add('was-validated')
                }, false)
            })
    })();

    // Add phone number formatting
    document.getElementById('phoneNumber').addEventListener('input', function(e) {
        var x = e.target.value.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/)
        e.target.value = !x[2] ? x[1] : '(' + x[1] + ') ' + x[2] + (x[3] ? '-' + x[3] : '')
    });

    // Add email validation
    document.getElementById('repEmail').addEventListener('input', function(e) {
        var email = e.target.value
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(email)) {
            e.target.setCustomValidity('Please enter a valid email address')
        } else {
            e.target.setCustomValidity('')
        }
    });

    // Add validation for preferred meeting time checkboxes
    function validatePreferredMeetingTime() {
        const checkboxes = document.querySelectorAll('.preferred-meeting');
        let isChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);

        checkboxes.forEach(checkbox => {
            if (!isChecked) {
                checkbox.setCustomValidity('Please select at least one preferred meeting time');
            } else {
                checkbox.setCustomValidity('');
            }
        });
    }

    // Add event listeners to preferred meeting time checkboxes
    document.querySelectorAll('.preferred-meeting').forEach(checkbox => {
        checkbox.addEventListener('change', validatePreferredMeetingTime);
    });

    // Initial validation check
    validatePreferredMeetingTime();
</script>