<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['ggc_user_id'])) {
    wp_redirect(home_url('/ggc-login/'));
    exit;
}
?>

<!-- members area -->
<div class="about-area py-120">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="site-heading mb-5">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="site-title-tagline">Registered Members</span>
                            <h2 class="site-title">
                                Conference <span>Participants</span>
                            </h2>
                        </div>
                        <div>
                            <a href="<?php echo home_url('/ggc-logout/'); ?>" class="theme-btn"><i class="far fa-sign-out"></i> Logout</a>
                        </div>
                    </div>
                </div>
                <?php
                global $wpdb;
                $table_name = $wpdb->prefix . 'user_registration';
                $results = $wpdb->get_results("SELECT * FROM $table_name");
                
                if ($results) :
                ?>
                <div class="table-responsive wow fadeInUp" data-wow-delay=".25s">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Company Name</th>
                                <th>Representative</th>
                                <th>Country</th>
                                <th>Industry Sectors</th>
                                <th>Business Type</th>
                                <th>Attending Days</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row) : ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($row->company_name); ?></strong><br>
                                    <small><?php echo esc_html($row->business_description); ?></small>
                                </td>
                                <td>
                                    <?php echo esc_html($row->rep_name); ?><br>
                                    <small><?php echo esc_html($row->rep_title); ?></small>
                                </td>
                                <td><?php echo esc_html($row->country); ?></td>
                                <td><?php echo esc_html($row->industry_sectors); ?></td>
                                <td><?php echo esc_html($row->company_type); ?></td>
                                <td>
                                    <?php
                                    $days = [];
                                    if ($row->attending_day1) $days[] = 'Day 1';
                                    if ($row->attending_day2) $days[] = 'Day 2';
                                    if ($row->attending_day3) $days[] = 'Day 3';
                                    if ($row->attending_day4_visits) $days[] = 'Day 4 (Visits)';
                                    if ($row->attending_day4_closing) $days[] = 'Day 4 (Closing)';
                                    echo esc_html(implode(', ', $days));
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo esc_url(home_url('/ggc-register-view/?id=' . $row->id)); ?>" class="theme-btn btn-sm">View Details <i class="far fa-arrow-right"></i></a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else : ?>
                <div class="alert alert-info" role="alert">
                    No registrations found.
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<!-- members area end -->