<?php

/**
 * Template Name: Stripe Webhook Endpoint
 * Description: Receives Stripe webhook events
 */

// Disable theme rendering and send plain response
header('Content-Type: application/json');

function password_generate()
{
    $digits = '';
    for ($i = 0; $i < 5; $i++) {
        $digits .= mt_rand(1, 9);
    }
    return $digits;
}

require_once get_template_directory() . '/vendor/autoload.php'; // Path to stripe-php

$stripe_secret = 'sk_test_51OZfYnEZ5LMR9wTweebFTo7dtAGvfMfSelFaiPjsIUeNeBhmItHswoHRsKCfzsgfNpw61TgoHPRjibgwcezXhOLy00Tg3YIoIe';
$webhook_secret = 'whsec_iKuPMhHGuriHoxgtDajXnzW8xRp4fGbz';


\Stripe\Stripe::setApiKey($stripe_secret);

// Get the raw payload
$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? null;

try {
    $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $webhook_secret);
    $event_array = $event->jsonSerialize(); // Full payload for optional logging

    // Extract data only for successful checkout
    if ($event->type === 'checkout.session.completed') {
        $session = $event->data->object;

        // Get customer details (email, name, phone)
        $client_reference_id = $session->client_reference_id ?? '';
        $email = $session->customer_details->email ?? '';
        $name  = $session->customer_details->name ?? '';
        $phone = $session->customer_details->phone ?? '';
        $created_at = $session->created ?? '';
        $stripe_customer_id = $session->customer ?? '';

        // Get payment amount by retrieving the PaymentIntent
        $payment_intent_id = $session->payment_intent ?? null;
        $amount = 0;

        if ($payment_intent_id) {
            $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
            $amount = $payment_intent->amount_received / 100; // convert from cents
            $currency = strtoupper($payment_intent->currency);
        }

        // Fetch the line items for this session
        $line_items = \Stripe\Checkout\Session::allLineItems($session->id, ['limit' => 100]);

        foreach ($line_items->data as $item) {
            $product_name = $item->description;
            $quantity = $item->quantity;

            // Do something with the product name and quantity
            error_log("Product: $product_name, Quantity: $quantity");
        }

        // Insert into database
        global $wpdb;
        $table_name = 'user_payments';

        $data = array(
            'client_id' => $client_reference_id,
            'created_at' => $created_at,
            'product_name' => $product_name,
            'quantity' => $quantity,
            'amount'  => $amount,
            'currency' => $currency ?? '',
            'pay_email'   => $email,
            'pay_name'    => $name,
            'pay_phone'   => $phone,
            'stripe_customer_id' => $stripe_customer_id,
            'payload' => json_encode($event_array)
        );

        $format = array('%s', '%s', '%s', '%s', '%s', '%s');
        $result = $wpdb->insert($table_name, $data, $format);

        // -----------------------------------

        $table_name = 'user_registration';
        $password = password_generate();

        $data = array(
            'client_id' => $client_reference_id,
            'email'   => $email,
            'password' => $password
        );

        $format = array('%s', '%s', '%s', '%s', '%s', '%s');
        $result = $wpdb->insert($table_name, $data, $format);

        $message = "
        Hi {$name},<br><br>

        Thank you for registering for the <strong>Go Global Conference</strong>. <br><br>
        
        Your account has been successfully created.<br><br>

        Here are your login details:<br>
        <strong>Email:</strong> {$email}<br>
        <strong>Password:</strong> {$password}<br><br>

        Log in here to complete or update your business profile:<br>
        <a href='https://goglobalconference.malachilabs.online/ggc-login/'>https://goglobalconference.malachilabs.online/ggc-login/</a><br><br>


        If you have any questions, feel free to reach out.<br><br>
        Email: <a href='mailto:<EMAIL>'><EMAIL></a><br>
        Phone: <a href='tel:**********'>************</a><br>

        Best regards,<br>
        Go Global Conference Team
        ";


        // Send email
        $to = $email;
        $subject = 'Welcome to Go Global Conference!';
        $message = $message;
        $headers = array('Content-Type: text/html; charset=UTF-8');

        wp_mail($to, $subject, $message, $headers);
    }
} catch (\Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid webhook: ' . $e->getMessage()]);
    exit;
}

// ✅ Handle event types
switch ($event->type) {
    case 'checkout.session.completed':
        $session = $event->data->object;
        // Example: update order or log data
        error_log("Stripe session completed: " . $session->id);
        break;

    case 'payment_intent.succeeded':
        $intent = $event->data->object;
        error_log("Payment succeeded: " . $intent->id);
        break;

    default:
        error_log("Unhandled event: " . $event->type);
}

http_response_code(200);
echo json_encode(['status' => 'success']);
exit;
