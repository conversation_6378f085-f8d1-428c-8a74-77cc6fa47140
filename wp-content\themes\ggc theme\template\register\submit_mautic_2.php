<?php
echo '-----PING-----';

// Step 0: Set credentials and config
$clientId = '3_4eqwsqat994w88ss848wssocg0g88sgkc00c8s8884sow04kcc';
$clientSecret = '4e1l6l603h2ckscg0c00gg00o4kkgsocgo0cc8woowk4sgsw80';
$mauticBaseUrl = 'https://crm.goglobalconference.com';
$segmentId = '97';

// Step 1: Get Access Token
$tokenUrl = $mauticBaseUrl . '/oauth/v2/token';
$postFields = [
    'grant_type' => 'client_credentials',
    'client_id' => $clientId,
    'client_secret' => $clientSecret,
];

$ch = curl_init($tokenUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postFields));
$tokenResponse = curl_exec($ch);
curl_close($ch);

$tokenData = json_decode($tokenResponse, true);

if (!isset($tokenData['access_token'])) {
    die("Failed to get access token:\n" . $tokenResponse);
}

$accessToken = $tokenData['access_token'];

echo "Access Token: $accessToken<hr>";

// Step 2: Define contact info
$email = '<EMAIL>';
$firstName = 'testjason';
$lastName = 'testsmith';

try {
    // Step 3: Create/Update contact
    $contactData = [
        'email' => $email,
        'firstname' => $firstName,
        'lastname' => $lastName,
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . '/api/contacts/new');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($contactData));
    $contactResponse = curl_exec($ch);
    $contactData = json_decode($contactResponse, true);

    echo '<hr>$contactData<pre>';
    var_dump($contactData);
    echo '</pre><hr>';

    curl_close($ch);

    if (!isset($contactData['contact']['id'])) {
        die('Failed to create contact.');
    }

    $contactId = $contactData['contact']['id'];

    // Step 4: Add contact to segment
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . "/api/contacts/{$contactId}/segments/add/{$segmentId}");
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    $segmentResponse = curl_exec($ch);
    curl_close($ch);

    echo '<hr><pre>';
    var_dump($segmentResponse);
    echo '</pre><hr>';
} catch (Exception $e) {
    echo '<hr><pre>';
    var_dump($e);
    echo '</pre><hr>';
    echo 'Caught exception: ',  $e->getMessage(), "\n";
}
