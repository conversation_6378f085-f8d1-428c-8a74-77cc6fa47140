/*-------------------------
    WordPress AJAX Contact Form
---------------------------*/
jQuery(document).ready(function($) {
    $('#contact-form').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = $('#contact-submit-btn');
        var btnText = submitBtn.find('.btn-text');
        var btnLoading = submitBtn.find('.btn-loading');
        var messageDiv = $('.form-messege');

        // Get form data
        var formData = {
            action: 'submit_contact_form',
            name: form.find('input[name="name"]').val(),
            email: form.find('input[name="email"]').val(),
            subject: form.find('input[name="subject"]').val(),
            message: form.find('textarea[name="message"]').val(),
            contact_nonce: form.find('input[name="contact_nonce"]').val()
        };

        // Validate form data
        if (!formData.name || !formData.email || !formData.subject || !formData.message) {
            showMessage('Please fill in all required fields.', 'error');
            return;
        }

        // Validate email format
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            showMessage('Please enter a valid email address.', 'error');
            return;
        }

        // Show loading state
        submitBtn.prop('disabled', true);
        btnText.hide();
        btnLoading.show();
        messageDiv.removeClass('text-success text-danger').empty();

        // Submit form via AJAX
        $.ajax({
            url: contact_ajax.ajax_url,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showMessage(response.data, 'success');
                    form[0].reset(); // Reset form
                } else {
                    showMessage(response.data || 'An error occurred. Please try again.', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                showMessage('An error occurred. Please try again later.', 'error');
            },
            complete: function() {
                // Reset button state
                submitBtn.prop('disabled', false);
                btnText.show();
                btnLoading.hide();
            }
        });
    });

    function showMessage(message, type) {
        var messageDiv = $('.form-messege');
        var className = type === 'success' ? 'text-success' : 'text-danger';
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var icon = type === 'success' ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-triangle"></i>';

        messageDiv
            .removeClass('text-success text-danger')
            .addClass(className)
            .html('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                  icon + ' ' + message +
                  '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                  '</div>')
            .show();

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(function() {
                messageDiv.fadeOut();
            }, 5000);
        }

        // Scroll to message
        $('html, body').animate({
            scrollTop: messageDiv.offset().top - 100
        }, 500);
    }
});