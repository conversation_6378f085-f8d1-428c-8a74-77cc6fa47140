<?php

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log all requests for debugging
error_log("Contact form request received: " . print_r($_POST, true));

// Define some constants
define( "RECIPIENT_NAME", "Give To Jamaica" );
define( "RECIPIENT_EMAIL", "<EMAIL>" );

// Read the form values with better sanitization
$success = false;
$name = isset( $_POST['name'] ) ? trim(preg_replace( "/[^\.\-\' a-zA-Z0-9]/", "", $_POST['name'] )) : "";
$senderEmail = isset( $_POST['email'] ) ? trim(preg_replace( "/[^\.\-\_\@a-zA-Z0-9]/", "", $_POST['email'] )) : "";
$phone = isset( $_POST['phone'] ) ? trim(preg_replace( "/[^\.\-\_\@a-zA-Z0-9\(\)\+\s]/", "", $_POST['phone'] )) : "";
$services = isset( $_POST['services'] ) ? trim(preg_replace( "/[^\.\-\_\@a-zA-Z0-9\s]/", "", $_POST['services'] )) : "";
$subject = isset( $_POST['subject'] ) ? trim(preg_replace( "/[^\.\-\_\@a-zA-Z0-9\s]/", "", $_POST['subject'] )) : "";
$address = isset( $_POST['address'] ) ? trim(preg_replace( "/[^\.\-\_\@a-zA-Z0-9\s\,]/", "", $_POST['address'] )) : "";
$website = isset( $_POST['website'] ) ? trim(preg_replace( "/[^\.\-\_\@a-zA-Z0-9\/\:]/", "", $_POST['website'] )) : "";
$message = isset( $_POST['message'] ) ? trim(preg_replace( "/(From:|To:|BCC:|CC:|Subject:|Content-Type:)/i", "", $_POST['message'] )) : "";

// Log sanitized data
error_log("Sanitized data - Name: $name, Email: $senderEmail, Message length: " . strlen($message));

// Validate required fields
$errors = [];
if (empty($name)) {
    $errors[] = "Name is required";
}
if (empty($senderEmail)) {
    $errors[] = "Email is required";
} elseif (!filter_var($senderEmail, FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Invalid email format";
}
if (empty($message)) {
    $errors[] = "Message is required";
}

// If there are validation errors, display them
if (!empty($errors)) {
    $errorMsg = implode(", ", $errors);
    error_log("Contact form validation errors: " . $errorMsg);
    echo "<div class='inner error'><p class='error'>Validation errors: $errorMsg</p></div><!-- /.inner -->";
    exit;
}

// Build email content
$mail_subject = 'Contact request from ' . $name . ' - Give To Jamaica Website';

$body = "New contact form submission from Give To Jamaica website:\r\n";
$body .= "=================================================\r\n\r\n";
$body .= 'SENDER INFORMATION:\r\n';
$body .= 'Name: '. $name . "\r\n";
$body .= 'Email: '. $senderEmail . " (REPLY TO THIS EMAIL)\r\n";

if ($phone) {$body .= 'Phone: '. $phone . "\r\n"; }
if ($services) {$body .= 'Services: '. $services . "\r\n"; }
if ($subject) {$body .= 'Subject: '. $subject . "\r\n"; }
if ($address) {$body .= 'Address: '. $address . "\r\n"; }
if ($website) {$body .= 'Website: '. $website . "\r\n"; }

$body .= "\r\nMessage:\r\n" . $message;
$body .= "\r\n\r\n---\r\nSent from Give To Jamaica contact form";

// Set up email headers - DreamHost requires From header to use a domain email
$recipient = RECIPIENT_NAME . " <" . RECIPIENT_EMAIL . ">";
$headers = array();
// DreamHost requires the From address to be from the same domain
$headers[] = "From: " . RECIPIENT_NAME . " <" . RECIPIENT_EMAIL . ">";
$headers[] = "Reply-To: " . $senderEmail;
$headers[] = "X-Mailer: PHP/" . phpversion();
$headers[] = "MIME-Version: 1.0";
$headers[] = "Content-Type: text/plain; charset=UTF-8";
// Add sender info in body since we can't use their email in From header
$headers[] = "X-Original-Sender: " . $senderEmail;

$header_string = implode("\r\n", $headers);

// Log email attempt
error_log("Attempting to send email to: $recipient");
error_log("Email subject: $mail_subject");
error_log("Email headers: $header_string");

// Check if mail function is available
if (!function_exists('mail')) {
    error_log("Mail function is not available");
    echo "<div class='inner error'><p class='error'>Email service is not configured on this server.</p></div><!-- /.inner -->";
    exit;
}

// Attempt to send the email
$success = mail($recipient, $mail_subject, $body, $header_string);

if ($success) {
    error_log("Email sent successfully");
    echo "<div class='inner success'><p class='success'>Thanks for contacting us. We will contact you ASAP!</p></div><!-- /.inner -->";
} else {
    $last_error = error_get_last();
    error_log("Email failed to send. Last error: " . print_r($last_error, true));
    echo "<div class='inner error'><p class='error'>Failed to send email. Please try again or contact us directly at " . RECIPIENT_EMAIL . "</p></div><!-- /.inner -->";
}

?>