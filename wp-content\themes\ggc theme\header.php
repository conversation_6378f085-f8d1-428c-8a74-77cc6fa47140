<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>

    <header class="header">
        <!-- Header Top -->
        <?php if (is_active_sidebar('header-top-bar')) : ?>
            <?php dynamic_sidebar('header-top-bar'); ?>
        <?php else : ?>

        <?php endif; ?>

        <div class="main-navigation">
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid position-relative">
                    <?php if (has_custom_logo()) : ?>
                        <div class="navbar-brand">
                            <?php the_custom_logo(); ?>
                        </div>
                    <?php else : ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="navbar-brand">
                            <?php bloginfo('name'); ?>
                        </a>
                    <?php endif; ?>
                    <div class="mobile-menu-right">
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                            data-bs-target="#main_nav" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="navbar-toggler-mobile-icon"><i class="far fa-bars"></i></span>
                        </button>
                    </div>
                    <div class="collapse navbar-collapse" id="main_nav">
                        <ul class="navbar-nav">
                            <li id="home-link" class="nav-item"><a class="nav-link" href="/">Home</a></li>
                            <li class="nav-item"><a class="nav-link" href="/about-us/">About</a></li>
                            <li class="nav-item"><a class="nav-link" href="/b2b-matchmaking/">B2B Matchmaking</a></li>
                            <li class="nav-item"><a class="nav-link" href="/schedule/">Schedule</a></li>
                            <li class="nav-item"><a class="nav-link" href="/resources/">Resources</a></li>
                            <li class="nav-item"><a class="nav-link" href="/contact-us/">Contact</a></li>
                        </ul>
                        <div class="nav-right">
                            <div class="nav-right-btn">
                                <?php if ($_SESSION['ggc_login']) : ?>
                                    <a href="/ggc-register-view/" class="theme-btn">My Profile<i class="fas fa-arrow-right"></i></a>
                                    <a href="/ggc-logout/" class="theme-btn signin-btn">Logout<i class="fas fa-arrow-right"></i></a>
                                <?php else : ?>
                                    <a href="/ggc-login/" class="theme-btn signin-btn">Login<i class="fas fa-arrow-right"></i></a>
                                    <a href="/ggc-register/" class="theme-btn">Register<i class="fas fa-arrow-right"></i></a>
                                <?php endif; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>