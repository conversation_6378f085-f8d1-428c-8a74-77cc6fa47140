<?php
/*
Template Name: GGC Login Page
*/

// Handle login form submission
$login_error = '';
if (isset($_POST['login_submit'])) {

    if (!isset($_POST['login_nonce']) || !wp_verify_nonce($_POST['login_nonce'], 'ggc_login_nonce')) {
        wp_die('Security check failed.');
    }

    global $wpdb;
    $email = sanitize_text_field($_POST['email']);
    $password = $_POST['password'];

    $user = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM user_registration WHERE email = %s",
        $email
    ));

    if ($email && $password === $user->password) {
        // Set session or cookie to maintain login state
        $_SESSION['ggc_user_id'] = $user->client_id;
        $_SESSION['ggc_email'] = strtolower($user->email);
        $_SESSION['ggc_login'] = true;

        // Determine redirect based on username
        if ($email === '<EMAIL>') {
            // Admin user - redirect to current default location (members page)
            $_SESSION['ggc_admin'] = true;
            wp_redirect(home_url('/ggc-register-members/'));
        } else {
            // Regular user - redirect to registration form
            $_SESSION['ggc_admin'] = false;
            wp_redirect(home_url('/ggc-register-view/'));
        }
        exit;
    } else {
        $login_error = 'Invalid Email & Password Combination.';
    }
}

get_header(); ?>

<main class="main">
    <?php get_template_part('template/home/<USER>'); ?>
    <!-- login area -->
    <div class="login-area py-120">
        <div class="container">
            <div class="col-md-5 mx-auto">
                <div class="login-form">
                    <div class="login-header">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/hero/conference.png" alt="">
                        <?php if (!empty($login_error)) : ?>
                            <div class="login-error"><?php echo $login_error; ?></div>
                        <?php else : ?>
                            <div class="login-details">Login with information emailed from GGC.</div>
                        <?php endif; ?>
                    </div>
                    <form method="post" action="<?php echo esc_url($_SERVER['REQUEST_URI']); ?>">
                        <?php wp_nonce_field('ggc_login_nonce', 'login_nonce'); ?>
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-envelope"></i></span>
                            <input name="email" type="email" class="form-control" placeholder="Your Email">
                        </div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="far fa-key"></i></span>
                            <input name="password" type="password" class="form-control" placeholder="Your Password">
                        </div>
                        <div class="d-flex align-items-center">
                            <button name="login_submit" type="submit" class="theme-btn"><span class="far fa-sign-in"></span> Login</button>
                        </div>
                    </form>
                    <div class="login-footer">
                        <p class="mt-20"><a href="/ggc-forgot-password" class="forgot-password">Forgot Password?</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>