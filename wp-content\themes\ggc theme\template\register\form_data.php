<?php
// First check if this user already has a registration record
global $wpdb;
$table_name = 'user_registration';

// Look up by database record ID
$existing_data = $wpdb->get_row($wpdb->prepare("
    SELECT * FROM $table_name
    WHERE client_id = %s
    LIMIT 1
", $_SESSION['ggc_user_id']));

// Helper function to get field value from existing data
function get_field_value($existing_data, $field_name, $default = '')
{
    return !empty($existing_data) && isset($existing_data->$field_name) ? $existing_data->$field_name : $default;
}

// Helper function to check if checkbox should be checked
function is_checked($existing_data, $field_name, $value)
{
    if (empty($existing_data) || !isset($existing_data->$field_name)) {
        return false;
    }
    $field_values = explode(',', $existing_data->$field_name);
    return in_array($value, $field_values);
}

// Helper function to check if radio button should be selected
function is_selected($existing_data, $field_name, $value)
{
    return !empty($existing_data) && isset($existing_data->$field_name) && $existing_data->$field_name === $value;
}
?>

<div class="col-lg-12 mt-3">
    <div class="site-heading text-center wow fadeInDown" data-wow-delay=".25s" style="visibility: visible; animation-delay: 0.25s; animation-name: fadeInDown;">
        <span class="site-title-tagline">B2B Matchmaking</span>
        <h2 class="site-title">Registration <span>Form</span></h2>
        <p>Give yourself approximately 10 minutes to complete this form.</p>
    </div>
</div>
<div class="col-lg-12 mt-3 px-5">
    <form id="dayForm" class="needs-validation" method="POST" action="<?php echo esc_url($_SERVER['REQUEST_URI']); ?>" enctype="multipart/form-data" novalidate>
        <?php wp_nonce_field('register_user_nonce'); ?>
        <div class="container-fluid" id="registerForm">

            <h3>Personal Information</h3>
            <div class="row g-3">
                <?php $responsive = "col-12 col-sm-12 col-md-6 col-lg-4" ?>
                <div class="<?= $responsive ?>">
                    <label for="firstName" class="form-label">First Name *</label>
                    <input type="text" class="form-control" name="firstName" id="firstName" value="<?php echo esc_attr(get_field_value($existing_data, 'first_name')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your first name.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="lastName" class="form-label">Last Name *</label>
                    <input type="text" class="form-control" name="lastName" id="lastName" value="<?php echo esc_attr(get_field_value($existing_data, 'last_name')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your last name.
                    </div>
                </div>

                <!-- ------------------------------------------------------------ -->

                <div class="<?= $responsive ?>">
                    <label for="phoneNumber" class="form-label">Contact Phone Number: WhatsApp or SMS. *</label>
                    <input type="text" class="form-control" name="phoneNumber" id="phoneNumber" value="<?php echo esc_attr(get_field_value($existing_data, 'contact_phone')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your phone number.
                    </div>
                </div>

                <!-- ------------------------------------------------------------ -->

                <div class="<?= $responsive ?>">
                    <label for="preferredLanguage" class="form-label">Preferred Language *</label>
                    <input type="text" class="form-control" name="preferredLanguage" id="preferredLanguage" value="<?php echo esc_attr(get_field_value($existing_data, 'preferred_language')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your preferred language.
                    </div>
                </div>

                <!-- ------------------------------------------------------------ -->

                <div class="<?= $responsive ?>">
                    <label for="country" class="form-label">Country *</label>
                    <input type="text" class="form-control" name="country" id="country" value="<?php echo esc_attr(get_field_value($existing_data, 'country')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your country.
                    </div>
                </div>

                <!-- ------------------------------------------------------------ -->

                <div class="<?= $responsive ?>">
                    <label for="address" class="form-label">City, State, ZIP Code *</label>
                    <input type="text" class="form-control" name="address" id="address" value="<?php echo esc_attr(get_field_value($existing_data, 'city_state_zip')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your address.
                    </div>
                </div>
            </div>

            <h3>Business Information</h3>
            <div class="row g-3">
                <div class="<?= $responsive ?>">
                    <label for="companyName" class="form-label">Company Name *</label>
                    <input type="text" class="form-control" name="companyname" id="companyName" value="<?php echo esc_attr(get_field_value($existing_data, 'company_name')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter company name.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="companyWebsite" class="form-label">Company Website</label>
                    <input type="text" class="form-control" name="companyWebsite" id="companyWebsite" value="<?php echo esc_attr(get_field_value($existing_data, 'company_website')); ?>">
                </div>
                <div class="<?= $responsive ?>">
                    <label for="linkedIn" class="form-label">LinkedIn Profile URL *</label>
                    <input type="text" class="form-control" name="linkedIn" id="linkedIn" value="<?php echo esc_attr(get_field_value($existing_data, 'linkedin_url')); ?>" required>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="socialMedia" class="form-label">Other Social Media Links</label>
                    <input type="text" class="form-control" name="socialMedia" id="socialMedia" value="<?php echo esc_attr(get_field_value($existing_data, 'social_media_links')); ?>" required>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="describeBusiness" class="form-label">Describe your business and its core focus *</label>
                    <input type="text" class="form-control" name="describeBusiness" id="describeBusiness" value="<?php echo esc_attr(get_field_value($existing_data, 'business_description')); ?>" required>
                    <div class="invalid-feedback">
                        Please describe your business.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="yearsBusiness" class="form-label">Years in Operation *</label>
                    <input type="number" class="form-control" id="yearsBusiness" name="yearsBusiness" value="<?php echo esc_attr(get_field_value($existing_data, 'years_in_operation')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter years in operation.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="numEmployees" class="form-label">Number of Employees *</label>
                    <input type="number" class="form-control" name="numEmployees" id="numEmployees" value="<?php echo esc_attr(get_field_value($existing_data, 'number_of_employees')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter number of employees.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="annualRevenue" class="form-label">Annual Revenue *</label>
                    <input type="text" class="form-control" name="annualRevenue" id="annualRevenue" value="<?php echo esc_attr(get_field_value($existing_data, 'annual_revenue')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your annual revenue.
                    </div>
                </div>
            </div>

            <h3>Representative Attending</h3>
            <div class="row g-3">
                <div class="<?= $responsive ?>">
                    <label for="repName" class="form-label">Full Name of Representative Attending *</label>
                    <input type="text" class="form-control" name="repName" id="repName" value="<?php echo esc_attr(get_field_value($existing_data, 'rep_name')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your representative's name.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="repTitle" class="form-label">Title of Representative Attending *</label>
                    <input type="text" class="form-control" name="repTitle" id="repTitle" value="<?php echo esc_attr(get_field_value($existing_data, 'rep_title')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your representative's title.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="repEmail" class="form-label">Email Address of Representative Attending *</label>
                    <input type="text" class="form-control" id="repEmail" name="repEmail" value="<?php echo esc_attr(get_field_value($existing_data, 'rep_email')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your representative's email.
                    </div>
                </div>
                <div class="<?= $responsive ?>">
                    <label for="repPhone" class="form-label">Phone Number of Representative Attending *</label>
                    <input type="text" class="form-control" name="repPhone" id="repPhone" value="<?php echo esc_attr(get_field_value($existing_data, 'rep_phone')); ?>" required>
                    <div class="invalid-feedback">
                        Please enter your representative's phone number.
                    </div>
                </div>
            </div>

            <h3>Program Participation</h3>
            <div class="row g-3 myml0">

                <!-- ------------------------------------------------------------ -->

                <label class="form-label ans-block">Please select the conference days you will be participating in and available for during the conference. (Select all that apply)
                </label>

                <div class="form-check col-12 col-sm-12 col-md-6">
                    <input class="form-check-input conference-day-participation" name="conference-days-participating[]" type="checkbox" value="Day 1 - Welcome Reception (6:00 PM - 10:00 PM)" id="day1_welcome_reception" <?php echo (!empty($existing_data) && $existing_data->attending_day1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="day1_welcome_reception">
                        Day 1 - Welcome Reception (6:00 PM - 10:00 PM)
                    </label>
                </div>
                <div class="form-check col-12 col-sm-12 col-md-6">
                    <input class="form-check-input conference-day-participation" name="conference-days-participating[]" type="checkbox" value="Day 2 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)" id="day2_b2b_matchmaking_sessions" <?php echo (!empty($existing_data) && $existing_data->attending_day2) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="day2_b2b_matchmaking_sessions">
                        Day 2 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)
                    </label>
                </div>
                <div class="form-check col-12 col-sm-12 col-md-6">
                    <input class="form-check-input conference-day-participation" name="conference-days-participating[]" type="checkbox" value="Day 3 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)" id="day3_b2b_matchmaking_sessions" <?php echo (!empty($existing_data) && $existing_data->attending_day3) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="day3_b2b_matchmaking_sessions">
                        Day 3 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)
                    </label>
                </div>
                <div class="form-check col-12 col-sm-12 col-md-6">
                    <input class="form-check-input conference-day-participation" name="conference-days-participating[]" type="checkbox" value="Day 4 - Site Visits (International Attendees ONLY)" id="day4_site_visits" <?php echo (!empty($existing_data) && $existing_data->attending_day4_visits) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="day4_site_visits">
                        Day 4 - Site Visits (International Attendees ONLY)
                    </label>
                </div>
                <div class="form-check col-12 col-sm-12 col-md-6">
                    <input class="form-check-input conference-day-participation" name="conference-days-participating[]" type="checkbox" value="Day 4 - Closing Reception (5:00 PM - 9:00 PM)" id="day4_closing_reception" <?php echo (!empty($existing_data) && $existing_data->attending_day4_closing) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="day4_closing_reception">
                        Day 4 - Closing Reception (5:00 PM - 9:00 PM)
                    </label>
                </div>

                <div class="invalid-feedback" id="conference-day-participation">
                    Please select at least one.
                </div>

                <!-- ------------------------------------------------------------ -->
                <hr>
                <label class="form-label ans-block">What are your primary objectives for attending? (Select up to 3)</label>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Buyer" id="primary-objective-buyer" <?php echo is_checked($existing_data, 'primary_objectives', 'Buyer') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-buyer">
                        Buyer
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Seller" id="primary-objective-seller" <?php echo is_checked($existing_data, 'primary_objectives', 'Seller') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-seller">
                        Seller
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Importer" id="primary-objective-importer" <?php echo is_checked($existing_data, 'primary_objectives', 'Importer') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-importer">
                        Importer
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Distributor" id="primary-objective-distributor" <?php echo is_checked($existing_data, 'primary_objectives', 'Distributor') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-distributor">
                        Distributor
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Retailer" id="primary-objective-retailer" <?php echo is_checked($existing_data, 'primary_objectives', 'Retailer') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-retailer">
                        Retailer
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Retail Investor" id="primary-objective-retail-investor" <?php echo is_checked($existing_data, 'primary_objectives', 'Retail Investor') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-retail-investor">
                        Retail Investor
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Institutional Investor" id="primary-objective-institutional-investor" <?php echo is_checked($existing_data, 'primary_objectives', 'Institutional Investor') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-institutional-investor">
                        Institutional Investor
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Fund Manager" id="primary-objective-fund-manager" <?php echo is_checked($existing_data, 'primary_objectives', 'Fund Manager') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-fund-manager">
                        Fund Manager
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Wholesaler" id="primary-objective-wholesaler" <?php echo is_checked($existing_data, 'primary_objectives', 'Wholesaler') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-wholesaler">
                        Wholesaler
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Joint Venture / Business Partner" id="primary-objective-joint-venture" <?php echo is_checked($existing_data, 'primary_objectives', 'Joint Venture / Business Partner') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-joint-venture">
                        Joint Venture / Business Partner
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Local Partner" id="primary-objective-local-partner" <?php echo is_checked($existing_data, 'primary_objectives', 'Local Partner') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-local-partner">
                        Local Partner
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Government" id="primary-objective-government" <?php echo is_checked($existing_data, 'primary_objectives', 'Government') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-government">
                        Government
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input primary-objective" name="primary_objectives[]" type="checkbox" value="Other" id="primary-objective-other" <?php echo is_checked($existing_data, 'primary_objectives', 'Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="primary-objective-other">
                        Other
                    </label>
                </div>

                <div class="invalid-feedback" id="primary-objective">
                    Please select at least one.
                </div>

                <!-- ------------------------------------------------------------ -->
                <hr>
                <label class="form-label ans-block">Will you be attending as a:</label>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-buyer" value="Buyer" <?php echo is_selected($existing_data, 'attendee_type', 'Buyer') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-buyer">
                        Buyer
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-seller" value="Seller" <?php echo is_selected($existing_data, 'attendee_type', 'Seller') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-seller">
                        Seller
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-investor" value="Investor" <?php echo is_selected($existing_data, 'attendee_type', 'Investor') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-investor">
                        Investor
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-seeking" value="Seeking Investment" <?php echo is_selected($existing_data, 'attendee_type', 'Seeking Investment') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-seeking">
                        Seeking Investment
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-partner" value="Partner" <?php echo is_selected($existing_data, 'attendee_type', 'Partner') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-partner">
                        Partner
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-both-buyer-seller" value="Both Buyer & Seller" <?php echo is_selected($existing_data, 'attendee_type', 'Both Buyer & Seller') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-both-buyer-seller">
                        Both Buyer & Seller
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-facilitator" value="Facilitator" <?php echo is_selected($existing_data, 'attendee_type', 'Facilitator') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-facilitator">
                        Facilitator
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="radio" name="attendee_type" id="attending-as-government" value="Government" <?php echo is_selected($existing_data, 'attendee_type', 'Government') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="attending-as-government">
                        Government
                    </label>
                </div>

                <div class="invalid-feedback">
                    Please select an option.
                </div>

                <label for="need-translation" class="form-label ans-block-special">Will you need translation or interpretation support? (No/Yes - If yes, specify for which language) *</label>
                <input type="text" class="form-control mt-0" name="need_translation" id="need-translation" value="<?php echo esc_attr(get_field_value($existing_data, 'need_translation') ? 'Yes' : 'No'); ?>" required>
                <div class="invalid-feedback">
                    Please enter yes or no.
                </div>

                <label for="translation-language" class="form-label ans-block-special">Translation Language (if applicable)</label>
                <input type="text" class="form-control mt-0" name="translation_language" id="translation-language" value="<?php echo esc_attr(get_field_value($existing_data, 'translation_language')); ?>">


            </div>
            <!-- ------------------------------------------------------------ -->

            <h3>Trade Activity</h3>

            <div class="row g-3 myml0">

                <label class="form-label ans-block">Is your company currently importing products or services? If so, from where? (Select all that apply)</label>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="United States" id="united-states" <?php echo is_checked($existing_data, 'importing_regions', 'United States') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="united-states">
                        United States
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Europe" id="europe" <?php echo is_checked($existing_data, 'importing_regions', 'Europe') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="europe">
                        Europe
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Latin America" id="latin-america" <?php echo is_checked($existing_data, 'importing_regions', 'Latin America') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="latin-america">
                        Latin America
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Africa" id="africa" <?php echo is_checked($existing_data, 'importing_regions', 'Africa') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="africa">
                        Africa
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Canada or Mexico" id="canada-mexico" <?php echo is_checked($existing_data, 'importing_regions', 'Canada or Mexico') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="canada-mexico">
                        Canada or Mexico
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Australia" id="australia" <?php echo is_checked($existing_data, 'importing_regions', 'Australia') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="australia">
                        Australia
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Asia" id="asia" <?php echo is_checked($existing_data, 'importing_regions', 'Asia') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="asia">
                        Asia
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Not currently importing" id="not-importing" <?php echo is_checked($existing_data, 'importing_regions', 'Not currently importing') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="not-importing">
                        Not currently importing
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="importing_regions[]" value="Other" id="other-importing" <?php echo is_checked($existing_data, 'importing_regions', 'Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="other-importing">
                        Other
                    </label>
                </div>

                <!-- ------------------------------------------------------------ -->
                <hr>
                <label class="form-label ans-block">Is your company currently exporting products or services? If so, to where? (Select all that apply)</label>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="United States" id="currently-exporting-products-or-services-united-states" <?php echo is_checked($existing_data, 'exporting_regions', 'United States') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-united-states">
                        United States
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Europe" id="currently-exporting-products-or-services-europe" <?php echo is_checked($existing_data, 'exporting_regions', 'Europe') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-europe">
                        Europe
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Latin America" id="currently-exporting-products-or-services-latin-america" <?php echo is_checked($existing_data, 'exporting_regions', 'Latin America') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-latin-america">
                        Latin America
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Africa" id="currently-exporting-products-or-services-africa" <?php echo is_checked($existing_data, 'exporting_regions', 'Africa') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-africa">
                        Africa
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Canada or Mexico" id="currently-exporting-products-or-services-canada-mexico" <?php echo is_checked($existing_data, 'exporting_regions', 'Canada or Mexico') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-canada-mexico">
                        Canada or Mexico
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Australia" id="currently-exporting-products-or-services-australia" <?php echo is_checked($existing_data, 'exporting_regions', 'Australia') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-australia">
                        Australia
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Asia" id="currently-exporting-products-or-services-asia" <?php echo is_checked($existing_data, 'exporting_regions', 'Asia') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-asia">
                        Asia
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Not currently exporting" id="currently-exporting-products-or-services-not-exporting" <?php echo is_checked($existing_data, 'exporting_regions', 'Not currently exporting') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-not-exporting">
                        Not currently exporting
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" name="exporting_regions[]" type="checkbox" value="Other" id="currently-exporting-products-or-services-other" <?php echo is_checked($existing_data, 'exporting_regions', 'Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="currently-exporting-products-or-services-other">
                        Other
                    </label>
                </div>

                <!-- ------------------------------------------------------------ -->


                <label for="geographic-areas-serving" class="form-label ans-block-special">Please explain the geographic areas in which you are serving with your products/services. *</label>
                <input type="text" class="form-control mt-0" name="service_areas" id="geographic-areas-serving" value="<?php echo esc_attr(get_field_value($existing_data, 'service_areas')); ?>" required>
                <div class="invalid-feedback">
                    Please enter the geographic area's where you serve your products.
                </div>


                <!-- ------------------------------------------------------------ -->

                <label class="form-label ans-block">Will you be exhibiting or part of a country/company pavilion? *</label>

                <div class="form-check col-6 col-sm-6 col-md-4">
                    <input class="form-check-input" type="radio" name="exhibiting_option" id="part-of-country-pavilion-exhibiting" value="Exhibiting" <?php echo is_selected($existing_data, 'exhibiting_option', 'Exhibiting') ? 'checked' : ''; ?> required>
                    <label class="form-check-label" for="part-of-country-pavilion-exhibiting">
                        Exhibiting
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-4">
                    <input class="form-check-input" type="radio" name="exhibiting_option" id="part-of-country-pavilion-country-pavilion" value="Country/Chamber/County Pavilion" <?php echo is_selected($existing_data, 'exhibiting_option', 'Country/Chamber/County Pavilion') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="part-of-country-pavilion-country-pavilion">
                        Country/Chamber/County Pavilion
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-4">
                    <input class="form-check-input" type="radio" name="exhibiting_option" id="part-of-country-pavilion-business-participation-only" value="Business Participation Only" <?php echo is_selected($existing_data, 'exhibiting_option', 'Business Participation Only') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="part-of-country-pavilion-business-participation-only">
                        Business Participation Only
                    </label>
                </div>

                <div class="invalid-feedback">
                    Please select an option.
                </div>
            </div>

            <!-- ------------------------------------------------------------ -->

            <h3>Company Profile and Objectives</h3>

            <div class="row g-3 myml0">

                <label for="looking-for-in-matchmaking" class="form-label ans-block-special">Tell us what you are looking for in this B2B Matchmaking event at the Go Global Conference. *</label>
                <input type="text" name="matchmaking_objectives" class="form-control mt-0" id="looking-for-in-matchmaking" value="<?php echo esc_attr(get_field_value($existing_data, 'matchmaking_objectives')); ?>" required>
                <div class="invalid-feedback">
                    Please enter what you are looking for in this B2B Matchmaking.
                </div>

                <label class="form-label ans-block">What industry/sector best describes your business? (Select up to 3)</label>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Agriculture" id="industry-sector-agriculture" <?php echo is_checked($existing_data, 'industry_sectors', 'Agriculture') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-agriculture">
                        Agriculture
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Automotive" id="industry-sector-automotive" <?php echo is_checked($existing_data, 'industry_sectors', 'Automotive') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-automotive">
                        Automotive
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Biotechnology" id="industry-sector-biotechnology" <?php echo is_checked($existing_data, 'industry_sectors', 'Biotechnology') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-biotechnology">
                        Biotechnology
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Construction & Infrastructure" id="industry-sector-construction-infrastructure" <?php echo is_checked($existing_data, 'industry_sectors', 'Construction & Infrastructure') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-construction-infrastructure">
                        Construction & Infrastructure
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Defense & Aerospace" id="industry-sector-defense-aerospace" <?php echo is_checked($existing_data, 'industry_sectors', 'Defense & Aerospace') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-defense-aerospace">
                        Defense & Aerospace
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="E-commerce" id="industry-sector-e-commerce" <?php echo is_checked($existing_data, 'industry_sectors', 'E-commerce') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-e-commerce">
                        E-commerce
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Education & EdTech" id="industry-sector-education-edtech" <?php echo is_checked($existing_data, 'industry_sectors', 'Education & EdTech') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-education-edtech">
                        Education & EdTech
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Energy & Utilities" id="industry-sector-energy-utilities" <?php echo is_checked($existing_data, 'industry_sectors', 'Energy & Utilities') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-energy-utilities">
                        Energy & Utilities
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Environmental Services" id="industry-sector-environmental-services" <?php echo is_checked($existing_data, 'industry_sectors', 'Environmental Services') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-environmental-services">
                        Environmental Services
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Entertainment & Media" id="industry-sector-entertainment-media" <?php echo is_checked($existing_data, 'industry_sectors', 'Entertainment & Media') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-entertainment-media">
                        Entertainment & Media
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Fashion & Apparel" id="industry-sector-fashion-apparel" <?php echo is_checked($existing_data, 'industry_sectors', 'Fashion & Apparel') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-fashion-apparel">
                        Fashion & Apparel
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Financial Technology (FinTech)" id="industry-sector-financial-technology" <?php echo is_checked($existing_data, 'industry_sectors', 'Financial Technology (FinTech)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-financial-technology">
                        Financial Technology (FinTech)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Food Technology (FoodTech)" id="industry-sector-food-technology" <?php echo is_checked($existing_data, 'industry_sectors', 'Food Technology (FoodTech)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-food-technology">
                        Food Technology (FoodTech)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Fragrances & Beauty" id="industry-sector-fragrances-beauty" <?php echo is_checked($existing_data, 'industry_sectors', 'Fragrances & Beauty') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-fragrances-beauty">
                        Fragrances & Beauty
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Healthcare & Medical Technology (HealthTech)" id="industry-sector-healthcare-medical-technology" <?php echo is_checked($existing_data, 'industry_sectors', 'Healthcare & Medical Technology (HealthTech)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-healthcare-medical-technology">
                        Healthcare & Medical Technology (HealthTech)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Hospitality & Tourism" id="industry-sector-hospitality-tourism" <?php echo is_checked($existing_data, 'industry_sectors', 'Hospitality & Tourism') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-hospitality-tourism">
                        Hospitality & Tourism
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Information Technology & Software Development" id="industry-sector-information-technology" <?php echo is_checked($existing_data, 'industry_sectors', 'Information Technology & Software Development') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-information-technology">
                        Information Technology & Software Development
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Logistics & Supply Chain" id="industry-sector-logistics-supply-chain" <?php echo is_checked($existing_data, 'industry_sectors', 'Logistics & Supply Chain') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-logistics-supply-chain">
                        Logistics & Supply Chain
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Manufacturing (including Advanced Manufacturing)" id="industry-sector-manufacturing" <?php echo is_checked($existing_data, 'industry_sectors', 'Manufacturing (including Advanced Manufacturing)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-manufacturing">
                        Manufacturing (including Advanced Manufacturing)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Marketing & Business Development" id="industry-sector-marketing-business-development" <?php echo is_checked($existing_data, 'industry_sectors', 'Marketing & Business Development') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-marketing-business-development">
                        Marketing & Business Development
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Mining & Natural Resources" id="industry-sector-mining-natural-resources" <?php echo is_checked($existing_data, 'industry_sectors', 'Mining & Natural Resources') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-mining-natural-resources">
                        Mining & Natural Resources
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Pharmaceuticals" id="industry-sector-pharmaceuticals" <?php echo is_checked($existing_data, 'industry_sectors', 'Pharmaceuticals') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-pharmaceuticals">
                        Pharmaceuticals
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Printing & Promotional Materials" id="industry-sector-printing-promotional-materials" <?php echo is_checked($existing_data, 'industry_sectors', 'Printing & Promotional Materials') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-printing-promotional-materials">
                        Printing & Promotional Materials
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Property Management" id="industry-sector-property-management" <?php echo is_checked($existing_data, 'industry_sectors', 'Property Management') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-property-management">
                        Property Management
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Real Estate & Property Development" id="industry-sector-real-estate-property-development" <?php echo is_checked($existing_data, 'industry_sectors', 'Real Estate & Property Development') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-real-estate-property-development">
                        Real Estate & Property Development
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Renewable Energy" id="industry-sector-renewable-energy" <?php echo is_checked($existing_data, 'industry_sectors', 'Renewable Energy') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-renewable-energy">
                        Renewable Energy
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Retail & Consumer Goods" id="industry-sector-retail-consumer-goods" <?php echo is_checked($existing_data, 'industry_sectors', 'Retail & Consumer Goods') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-retail-consumer-goods">
                        Retail & Consumer Goods
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Telecommunications" id="industry-sector-telecommunications" <?php echo is_checked($existing_data, 'industry_sectors', 'Telecommunications') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-telecommunications">
                        Telecommunications
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Transportation & Mobility" id="industry-sector-transportation-mobility" <?php echo is_checked($existing_data, 'industry_sectors', 'Transportation & Mobility') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-transportation-mobility">
                        Transportation & Mobility
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Water & Sanitation" id="industry-sector-water-sanitation" <?php echo is_checked($existing_data, 'industry_sectors', 'Water & Sanitation') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-water-sanitation">
                        Water & Sanitation
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Waste Management & Recycling" id="industry-sector-waste-management-recycling" <?php echo is_checked($existing_data, 'industry_sectors', 'Waste Management & Recycling') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-waste-management-recycling">
                        Waste Management & Recycling
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Wholesale & Distribution" id="industry-sector-wholesale-distribution" <?php echo is_checked($existing_data, 'industry_sectors', 'Wholesale & Distribution') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-wholesale-distribution">
                        Wholesale & Distribution
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Wine & Beverage" id="industry-sector-wine-beverage" <?php echo is_checked($existing_data, 'industry_sectors', 'Wine & Beverage') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-wine-beverage">
                        Wine & Beverage
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Cybersecurity" id="industry-sector-cybersecurity" <?php echo is_checked($existing_data, 'industry_sectors', 'Cybersecurity') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-cybersecurity">
                        Cybersecurity
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Artificial Intelligence (AI)" id="industry-sector-artificial-intelligence" <?php echo is_checked($existing_data, 'industry_sectors', 'Artificial Intelligence (AI)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-artificial-intelligence">
                        Artificial Intelligence (AI)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Internet of Things (IoT)" id="industry-sector-internet-of-things" <?php echo is_checked($existing_data, 'industry_sectors', 'Internet of Things (IoT)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-internet-of-things">
                        Internet of Things (IoT)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Cloud Computing" id="industry-sector-cloud-computing" <?php echo is_checked($existing_data, 'industry_sectors', 'Cloud Computing') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-cloud-computing">
                        Cloud Computing
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Managed Service Providers (MSPs)" id="industry-sector-managed-service-providers" <?php echo is_checked($existing_data, 'industry_sectors', 'Managed Service Providers (MSPs)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-managed-service-providers">
                        Managed Service Providers (MSPs)
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Talent & HR Solutions" id="industry-sector-talent-hr-solutions" <?php echo is_checked($existing_data, 'industry_sectors', 'Talent & HR Solutions') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-talent-hr-solutions">
                        Talent & HR Solutions
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Investment & Fund Management" id="industry-sector-investment-fund-management" <?php echo is_checked($existing_data, 'industry_sectors', 'Investment & Fund Management') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-investment-fund-management">
                        Investment & Fund Management
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="industry_sectors[]" value="Other" id="industry-sector-other" <?php echo is_checked($existing_data, 'industry_sectors', ' Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="industry-sector-other">
                        Other:
                    </label>
                </div>

                <!-- ------------------------------------------------------------ -->
                <hr>
                <label class="form-label ans-block">What option best describes your company type? (Select up to 3)</label>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Buyer" id="company-type-buyer" <?php echo is_checked($existing_data, 'company_type', 'Buyer') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-buyer">
                        Buyer
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Seller" id="company-type-seller" <?php echo is_checked($existing_data, 'company_type', 'Seller') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-seller">
                        Seller
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Wholesaler" id="company-type-wholesaler" <?php echo is_checked($existing_data, 'company_type', 'Wholesaler') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-wholesaler">
                        Wholesaler
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Distributor" id="company-type-distributor" <?php echo is_checked($existing_data, 'company_type', 'Distributor') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-distributor">
                        Distributor
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Retailer" id="company-type-retailer" <?php echo is_checked($existing_data, 'company_type', 'Retailer') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-retailer">
                        Retailer
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Lender" id="company-type-lender" <?php echo is_checked($existing_data, 'company_type', 'Lender') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-lender">
                        Lender
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Investor" id="company-type-investor" <?php echo is_checked($existing_data, 'company_type', 'Investor') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-investor">
                        Investor
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Government" id="company-type-government" <?php echo is_checked($existing_data, 'company_type', 'Government') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-government">
                        Government
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Non-profit" id="company-type-non-profit" <?php echo is_checked($existing_data, 'company_type', 'Non-profit') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-non-profit">
                        Non-profit
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input" type="checkbox" name="company_type[]" value="Other" id="company-type-other" <?php echo is_checked($existing_data, 'company_type', 'Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-type-other">
                        Other:
                    </label>
                </div>

                <label for="business-offering" class="form-label ans-block-special">What is your business offering to potential partners?
                    (Describe products, services, standout solutions, etc.) *
                </label>
                <input type="text" class="form-control mt-0" name="business_offering" id="business-offering" value="<?php echo esc_attr(get_field_value($existing_data, 'business_offering')); ?>" required>
                <div class="invalid-feedback">
                    Please enter business offering.
                </div>

                <label for="business-seeking" class="form-label ans-block-special">What is your business seeking from potential partners?
                    (E.g., investment, suppliers, service providers, distributors, etc.) *
                </label>
                <input type="text" class="form-control mt-0" name="business_seeking" id="business-seeking" value="<?php echo esc_attr(get_field_value($existing_data, 'business_seeking')); ?>" required>
                <div class="invalid-feedback">
                    Please enter what your business is seeking.
                </div>

                <label for="seeking" class="form-label ans-block-special">Please state the specific international suppliers, products, services, partnerships, or investment opportunities you're seeking from your match.
                    (Specify types, sectors, or regions of interest) *</label>
                <input type="text" class="form-control mt-0" name="specific_needs" id="seeking" value="<?php echo esc_attr(get_field_value($existing_data, 'specific_needs')); ?>" required>
                <div class="invalid-feedback">
                    Please answer this question.
                </div>

                <div class="form-label ans-block-special">What geographic areas or markets are you interested in expanding into?
                    (List countries or regions) *</div>

                <select class="form-select mt-0" name="expansion_markets[]" aria-label="Select Region" required>
                    <option value="" <?php echo empty(get_field_value($existing_data, 'expansion_markets')) ? 'selected' : ''; ?> disabled>Choose a Region</option>
                    <optgroup label="North America">
                        <option value="North America" <?php echo is_checked($existing_data, 'expansion_markets', 'North America') ? 'selected' : ''; ?>>North America</option>
                        <option value="United States" <?php echo is_checked($existing_data, 'expansion_markets', 'United States') ? 'selected' : ''; ?>>United States</option>
                        <option value="Canada" <?php echo is_checked($existing_data, 'expansion_markets', 'Canada') ? 'selected' : ''; ?>>Canada</option>
                        <option value="Mexico" <?php echo is_checked($existing_data, 'expansion_markets', 'Mexico') ? 'selected' : ''; ?>>Mexico</option>
                    </optgroup>
                    <optgroup label="Latin America & Caribbean">
                        <option value="Latin America & the Caribbean" <?php echo is_checked($existing_data, 'expansion_markets', 'Latin America & the Caribbean') ? 'selected' : ''; ?>>Latin America & the Caribbean</option>
                        <option value="Brazil" <?php echo is_checked($existing_data, 'expansion_markets', 'Brazil') ? 'selected' : ''; ?>>Brazil</option>
                        <option value="Argentina" <?php echo is_checked($existing_data, 'expansion_markets', 'Argentina') ? 'selected' : ''; ?>>Argentina</option>
                        <option value="Colombia" <?php echo is_checked($existing_data, 'expansion_markets', 'Colombia') ? 'selected' : ''; ?>>Colombia</option>
                        <option value="Chile" <?php echo is_checked($existing_data, 'expansion_markets', 'Chile') ? 'selected' : ''; ?>>Chile</option>
                        <option value="Caribbean (e.g., Jamaica, Trinidad & Tobago)" <?php echo is_checked($existing_data, 'expansion_markets', 'Caribbean (e.g., Jamaica, Trinidad & Tobago)') ? 'selected' : ''; ?>>Caribbean (e.g., Jamaica, Trinidad & Tobago)</option>
                        <option value="Other Latin American countries" <?php echo is_checked($existing_data, 'expansion_markets', 'Other Latin American countries') ? 'selected' : ''; ?>>Other Latin American countries</option>
                    </optgroup>
                    <optgroup label="Europe">
                        <option value="Europe" <?php echo is_checked($existing_data, 'expansion_markets', 'Europe') ? 'selected' : ''; ?>>Europe</option>
                        <option value="United Kingdom" <?php echo is_checked($existing_data, 'expansion_markets', 'United Kingdom') ? 'selected' : ''; ?>>United Kingdom</option>
                        <option value="Germany" <?php echo is_checked($existing_data, 'expansion_markets', 'Germany') ? 'selected' : ''; ?>>Germany</option>
                        <option value="France" <?php echo is_checked($existing_data, 'expansion_markets', 'France') ? 'selected' : ''; ?>>France</option>
                        <option value="Netherlands" <?php echo is_checked($existing_data, 'expansion_markets', 'Netherlands') ? 'selected' : ''; ?>>Netherlands</option>
                        <option value="Italy" <?php echo is_checked($existing_data, 'expansion_markets', 'Italy') ? 'selected' : ''; ?>>Italy</option>
                        <option value="Spain" <?php echo is_checked($existing_data, 'expansion_markets', 'Spain') ? 'selected' : ''; ?>>Spain</option>
                        <option value="Eastern Europe (e.g., Poland, Romania)" <?php echo is_checked($existing_data, 'expansion_markets', 'Eastern Europe (e.g., Poland, Romania)') ? 'selected' : ''; ?>>Eastern Europe (e.g., Poland, Romania)</option>
                        <option value="Nordic Countries (e.g., Sweden, Denmark, Finland)" <?php echo is_checked($existing_data, 'expansion_markets', 'Nordic Countries (e.g., Sweden, Denmark, Finland)') ? 'selected' : ''; ?>>Nordic Countries (e.g., Sweden, Denmark, Finland)</option>
                    </optgroup>
                    <optgroup label="Africa">
                        <option value="Sub-Saharan Africa" <?php echo is_checked($existing_data, 'expansion_markets', 'Sub-Saharan Africa') ? 'selected' : ''; ?>>Sub-Saharan Africa</option>
                        <option value="South Africa" <?php echo is_checked($existing_data, 'expansion_markets', 'South Africa') ? 'selected' : ''; ?>>South Africa</option>
                        <option value="Nigeria" <?php echo is_checked($existing_data, 'expansion_markets', 'Nigeria') ? 'selected' : ''; ?>>Nigeria</option>
                        <option value="Kenya" <?php echo is_checked($existing_data, 'expansion_markets', 'Kenya') ? 'selected' : ''; ?>>Kenya</option>
                        <option value="Ghana" <?php echo is_checked($existing_data, 'expansion_markets', 'Ghana') ? 'selected' : ''; ?>>Ghana</option>
                        <option value="Zambia" <?php echo is_checked($existing_data, 'expansion_markets', 'Zambia') ? 'selected' : ''; ?>>Zambia</option>
                        <option value="Zimbabwe" <?php echo is_checked($existing_data, 'expansion_markets', 'Zimbabwe') ? 'selected' : ''; ?>>Zimbabwe</option>
                        <option value="Rwanda" <?php echo is_checked($existing_data, 'expansion_markets', 'Rwanda') ? 'selected' : ''; ?>>Rwanda</option>
                        <option value="Ethiopia" <?php echo is_checked($existing_data, 'expansion_markets', 'Ethiopia') ? 'selected' : ''; ?>>Ethiopia</option>
                        <option value="Other African countries" <?php echo is_checked($existing_data, 'expansion_markets', 'Other African countries') ? 'selected' : ''; ?>>Other African countries</option>
                    </optgroup>
                    <optgroup label="Middle East & North Africa">
                        <option value="Middle East & North Africa (MENA)" <?php echo is_checked($existing_data, 'expansion_markets', 'Middle East & North Africa (MENA)') ? 'selected' : ''; ?>>Middle East & North Africa (MENA)</option>
                        <option value="United Arab Emirates" <?php echo is_checked($existing_data, 'expansion_markets', 'United Arab Emirates') ? 'selected' : ''; ?>>United Arab Emirates</option>
                        <option value="Saudi Arabia" <?php echo is_checked($existing_data, 'expansion_markets', 'Saudi Arabia') ? 'selected' : ''; ?>>Saudi Arabia</option>
                        <option value="Egypt" <?php echo is_checked($existing_data, 'expansion_markets', 'Egypt') ? 'selected' : ''; ?>>Egypt</option>
                        <option value="Morocco" <?php echo is_checked($existing_data, 'expansion_markets', 'Morocco') ? 'selected' : ''; ?>>Morocco</option>
                        <option value="Other MENA countries" <?php echo is_checked($existing_data, 'expansion_markets', 'Other MENA countries') ? 'selected' : ''; ?>>Other MENA countries</option>
                    </optgroup>
                    <optgroup label="Asia & Pacific">
                        <option value="Asia & Pacific" <?php echo is_checked($existing_data, 'expansion_markets', 'Asia & Pacific') ? 'selected' : ''; ?>>Asia & Pacific</option>
                        <option value="China" <?php echo is_checked($existing_data, 'expansion_markets', 'China') ? 'selected' : ''; ?>>China</option>
                        <option value="India" <?php echo is_checked($existing_data, 'expansion_markets', 'India') ? 'selected' : ''; ?>>India</option>
                        <option value="Japan" <?php echo is_checked($existing_data, 'expansion_markets', 'Japan') ? 'selected' : ''; ?>>Japan</option>
                        <option value="South Korea" <?php echo is_checked($existing_data, 'expansion_markets', 'South Korea') ? 'selected' : ''; ?>>South Korea</option>
                        <option value="Southeast Asia (e.g., Thailand, Vietnam, Philippines)" <?php echo is_checked($existing_data, 'expansion_markets', 'Southeast Asia (e.g., Thailand, Vietnam, Philippines)') ? 'selected' : ''; ?>>Southeast Asia (e.g., Thailand, Vietnam, Philippines)</option>
                        <option value="Australia" <?php echo is_checked($existing_data, 'expansion_markets', 'Australia') ? 'selected' : ''; ?>>Australia</option>
                        <option value="New Zealand" <?php echo is_checked($existing_data, 'expansion_markets', 'New Zealand') ? 'selected' : ''; ?>>New Zealand</option>
                    </optgroup>
                    <optgroup label="Other Regions">
                        <option value="Other Regions" <?php echo is_checked($existing_data, 'expansion_markets', 'Other Regions') ? 'selected' : ''; ?>>Other Regions</option>
                        <option value="Central Asia (e.g., Kazakhstan, Uzbekistan)" <?php echo is_checked($existing_data, 'expansion_markets', 'Central Asia (e.g., Kazakhstan, Uzbekistan)') ? 'selected' : ''; ?>>Central Asia (e.g., Kazakhstan, Uzbekistan)</option>
                        <option value="Russia & CIS" <?php echo is_checked($existing_data, 'expansion_markets', 'Russia & CIS') ? 'selected' : ''; ?>>Russia & CIS</option>
                        <option value="Global / Open to all markets" <?php echo is_checked($existing_data, 'expansion_markets', 'Global / Open to all markets') ? 'selected' : ''; ?>>Global / Open to all markets</option>
                    </optgroup>
                </select>
                <div class="invalid-feedback">
                    Please enter geographic areas.
                </div>

                <label for="expansion-region" class="form-label ans-block-special">Please specify the region or provide additional details about your expansion plans:</label>
                <input type="text" class="form-control mt-0" name="expansion_region" id="expansion-region" value="<?php echo esc_attr(get_field_value($existing_data, 'expansion_region')); ?>">

                <label for="program-benefit-you" class="form-label ans-block-special">How would this program benefit you or your company?</label>
                <input type="text" class="form-control mt-0" name="program_benefits" id="program-benefit-you" value="<?php echo esc_attr(get_field_value($existing_data, 'program_benefits')); ?>">

                <label for="companies-want-to-meet" class="form-label ans-block-special">What type of companies do you want to meet?</label>
                <input type="text" class="form-control mt-0" name="companies_to_meet" id="companies-want-to-meet" value="<?php echo esc_attr(get_field_value($existing_data, 'companies_to_meet')); ?>">



                <div class="form-label ans-block-special">What is your preferred meeting time during the event? *</div>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input preferred-meeting" type="checkbox" name="preferred_meeting_time[]" value="Morning (AM)" id="preferred-meeting-time-am" <?php echo is_checked($existing_data, 'preferred_meeting_time', 'Morning (AM)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="preferred-meeting-time-am">
                        Morning (AM)
                    </label>
                </div>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input preferred-meeting" type="checkbox" name="preferred_meeting_time[]" value="Afternoon (PM)" id="preferred-meeting-time-pm" <?php echo is_checked($existing_data, 'preferred_meeting_time', 'Afternoon (PM)') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="preferred-meeting-time-pm">
                        Afternoon (PM)
                    </label>
                </div>

                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input preferred-meeting" type="checkbox" name="preferred_meeting_time[]" value="Flexible" id="preferred-meeting-time-flexible" <?php echo is_checked($existing_data, 'preferred_meeting_time', 'Flexible') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="preferred-meeting-time-flexible">
                        Flexible
                    </label>
                </div>

                <div class="invalid-feedback" id="preferred-meeting-time-feedback">
                    Please select at least one preferred meeting time.
                </div>

            </div>

            <!-- ------------------------------------------------------------ -->

            <h3>Previous Participation & Referrals</h3>

            <div class="row g-3 myml0">

                <label class="form-label ans-block">Have you participated in a B2B Matchmaking meeting before?</label>

                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="b2bMatchParticipation" id="b2bYes" value="Yes" <?php echo (!empty($existing_data) && $existing_data->participated_before == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="b2bYes">Yes</label>
                </div>

                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="b2bMatchParticipation" id="b2bNo" value="No" <?php echo (!empty($existing_data) && $existing_data->participated_before == 0) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="b2bNo">No</label>
                </div>

                <div class="invalid-feedback">
                    Please select an option.
                </div>

                <label for="similar-programs-before" class="form-label ans-block-special">Have you attended similar programs before?(List name, date, and location of past programs)</label>
                <input type="text" class="form-control mt-0" name="previous_programs" id="similar-programs-before" value="<?php echo esc_attr(get_field_value($existing_data, 'previous_programs')); ?>">


                <div class="form-label ans-block">Select the organization(s) to which you belong *</div>
                <div class="form-check col-6 col-sm-6 col-md-3 mt-0">
                    <input class="form-check-input org-belong" type="checkbox" name="memberships[]" value="ABC Member" id="belong-abc-member" <?php echo is_checked($existing_data, 'memberships', 'ABC Member') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="belong-abc-member">
                        ABC Member
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3 mt-0">
                    <input class="form-check-input org-belong" type="checkbox" name="memberships[]" value="GOC Member" id="belong-goc-member" <?php echo is_checked($existing_data, 'memberships', 'GOC Member') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="belong-goc-member">
                        GOC Member
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3 mt-0">
                    <input class="form-check-input org-belong" type="checkbox" name="memberships[]" value="Binational Chamber of Commerce" id="belong-binational-chamber" <?php echo is_checked($existing_data, 'memberships', 'Binational Chamber of Commerce') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="belong-binational-chamber">
                        Binational Chamber of Commerce
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3 mt-0">
                    <input class="form-check-input org-belong" type="checkbox" name="memberships[]" value="International Trade Office/Department" id="belong-international-trade-office" <?php echo is_checked($existing_data, 'memberships', 'International Trade Office/Department') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="belong-international-trade-office">
                        International Trade Office/Department
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3 mt-0">
                    <input class="form-check-input org-belong" type="checkbox" name="memberships[]" value="Neither" id="belong-neither" <?php echo is_checked($existing_data, 'memberships', 'Neither') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="belong-neither">
                        Neither
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3 mt-0">
                    <input class="form-check-input org-belong" type="checkbox" name="memberships[]" value="Other" id="belong-other" <?php echo is_checked($existing_data, 'memberships', 'Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="belong-other">
                        Other
                    </label>
                </div>

                <div class="invalid-feedback" id="org-belong">
                    Please select at least one.
                </div>

                <!-- ------------------------------------------------------------ -->
                <hr>
                <label class="form-label ans-block">How did you hear about this opportunity? (Select up to 2) *</label>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Global Opportunities Committee" id="heard-about-goc" <?php echo is_checked($existing_data, 'referral_sources', 'Global Opportunities Committee') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-goc">
                        Global Opportunities Committee
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Atlanta Black Chambers" id="heard-about-atlanta-black-chambers" <?php echo is_checked($existing_data, 'referral_sources', 'Atlanta Black Chambers') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-atlanta-black-chambers">
                        Atlanta Black Chambers
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Social Media" id="heard-about-social-media" <?php echo is_checked($existing_data, 'referral_sources', 'Social Media') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-social-media">
                        Social Media
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Friend, Family, Colleague" id="heard-about-friend-family-colleague" <?php echo is_checked($existing_data, 'referral_sources', 'Friend, Family, Colleague') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-friend-family-colleague">
                        Friend, Family, Colleague
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Email List / Newsletter" id="heard-about-email-list" <?php echo is_checked($existing_data, 'referral_sources', 'Email List / Newsletter') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-email-list">
                        Email List / Newsletter
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Regional Lead" id="heard-about-regional-lead" <?php echo is_checked($existing_data, 'referral_sources', 'Regional Lead') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-regional-lead">
                        Regional Lead
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Go Global Conference" id="heard-about-go-global-conference" <?php echo is_checked($existing_data, 'referral_sources', 'Go Global Conference') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-go-global-conference">
                        Go Global Conference
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Binational Chambers" id="heard-about-binational-chambers" <?php echo is_checked($existing_data, 'referral_sources', 'Binational Chambers') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-binational-chambers">
                        Binational Chambers
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Local Chambers" id="heard-about-local-chambers" <?php echo is_checked($existing_data, 'referral_sources', 'Local Chambers') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-local-chambers">
                        Local Chambers
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Media" id="heard-about-media" <?php echo is_checked($existing_data, 'referral_sources', 'Media') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-media">
                        Media
                    </label>
                </div>
                <div class="form-check col-6 col-sm-6 col-md-3">
                    <input class="form-check-input heard-about" type="checkbox" name="referral_sources[]" value="Other" id="heard-about-other" <?php echo is_checked($existing_data, 'referral_sources', 'Other') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="heard-about-other">
                        Other
                    </label>
                </div>

                <div class="invalid-feedback" id="heard-about">
                    Please select at least one.
                </div>

                <label for="success-look-like" class="form-label ans-block-special">What does success at this year's event look like for you? *</label>
                <input type="text" class="form-control mt-0" name="success_description" id="success-look-like" value="<?php echo esc_attr(get_field_value($existing_data, 'success_description')); ?>" required>
                <div class="invalid-feedback">
                    Please describe success.
                </div>

                <label for="additional-questions" class="form-label ans-block-special">Do you have any additional questions or comments?</label>
                <input type="text" class="form-control mt-0" id="additional-questions" name="questions_comments" value="<?php echo esc_attr(get_field_value($existing_data, 'questions_comments')); ?>">

            </div>

            <!-- ------------------------------------------------------------ -->

            <h3>Uploads</h3>

            <div class="row g-3">
                <div class="col-6 col-sm-6 col-md-4">
                    <label for="business_profile" class="form-label">Upload Your Business Profile (PDF only) *</label>
                    <?php if (!empty($existing_data) && !empty($existing_data->business_profile_upload)): ?>
                        <div class="mb-2">
                            <small class="text-muted">Current file: <a href="<?php echo esc_url($existing_data->business_profile_upload); ?>" target="_blank">View Current Business Profile</a></small>
                        </div>
                    <?php endif; ?>
                    <input class="form-control" type="file" name="business_profile" id="business_profile" accept=".pdf" required>
                    <div class="invalid-feedback">
                        Please upload a Business Profile.
                    </div>
                </div>
                <div class="col-6 col-sm-6 col-md-4">
                    <label for="company_logo" class="form-label">Upload your Company Logo (PNG, JPG, JPEG) *</label>
                    <?php if (!empty($existing_data) && !empty($existing_data->company_logo_upload)): ?>
                        <div class="mb-2">
                            <small class="text-muted d-block mb-2">Current Company Logo:</small>
                            <img src="<?php echo esc_url($existing_data->company_logo_upload); ?>" alt="Current Company Logo" class="img-thumbnail" style="max-width: 150px; max-height: 100px; object-fit: contain;">
                        </div>
                    <?php endif; ?>
                    <input class="form-control" type="file" name="company_logo" id="company_logo" accept=".png,.jpg,.jpeg" required>
                    <div class="invalid-feedback">
                        Please upload a Company Logo.
                    </div>
                </div>
                <div class="col-6 col-sm-6 col-md-4">
                    <label for="headshot" class="form-label">Upload your Headshot (PNG, JPG, JPEG) *</label>
                    <?php if (!empty($existing_data) && !empty($existing_data->headshot_upload)): ?>
                        <div class="mb-2">
                            <small class="text-muted d-block mb-2">Current Headshot:</small>
                            <img src="<?php echo esc_url($existing_data->headshot_upload); ?>" alt="Current Headshot" class="img-thumbnail" style="max-width: 100px; max-height: 100px; object-fit: cover; border-radius: 50%;">
                        </div>
                    <?php endif; ?>
                    <input class="form-control" type="file" name="headshot" id="headshot" accept=".png,.jpg,.jpeg" required>
                    <div class="invalid-feedback">
                        Please upload a Headshot.
                    </div>
                </div>
            </div>

            <!-- ------------------------------------------------------------ -->

            <h3>Consent</h3>

            <div class="row g-3 myml0">

                <label class="form-label ans-block">Would you like your company to be highlighted in the event materials or media? *</label>

                <div class="form-check form-check-inline" style="width: 60px;">
                    <input class="form-check-input" type="radio" name="highlight_company" id="company-highlight-yes" value="Yes" <?php echo is_selected($existing_data, 'highlight_company', 'Yes') ? 'checked' : ''; ?> required>
                    <label class="form-check-label" for="company-highlight-yes">Yes</label>
                </div>
                <div class="form-check form-check-inline" style="width: 60px;">
                    <input class="form-check-input" type="radio" name="highlight_company" id="company-highlight-no" value="No" <?php echo is_selected($existing_data, 'highlight_company', 'No') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-highlight-no">No</label>
                </div>
                <div class="form-check form-check-inline" style="width: 60px;">
                    <input class="form-check-input" type="radio" name="highlight_company" id="company-highlight-maybe" value="Maybe" <?php echo is_selected($existing_data, 'highlight_company', 'Maybe') ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="company-highlight-maybe">Maybe</label>
                </div>
                <div class="invalid-feedback">
                    Please select an option.
                </div>

                <label class="form-label ans-block">Consent to store data and receive updates? *</label>

                <div class="form-check form-check-inline" style="width: 60px;">
                    <input class="form-check-input" type="radio" name="consent-to-store-data" id="consent-to-store-data-yes" value="Yes" <?php echo (!empty($existing_data) && $existing_data->consent_store_data == 1) ? 'checked' : ''; ?> required>
                    <label class="form-check-label" for="consent-to-store-data-yes">Yes</label>
                </div>
                <div class="form-check form-check-inline" style="width: 60px;">
                    <input class="form-check-input" type="radio" name="consent-to-store-data" id="consent-to-store-data-no" value="No" <?php echo (!empty($existing_data) && $existing_data->consent_store_data == 0) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="consent-to-store-data-no">No</label>
                </div>
                <div class="invalid-feedback">
                    Please select an option.
                </div>


                <!-- ------------------------------------------------------------ -->

                <label class="form-label ans-block">Confirm Participation</label>
                <div class="form-check col-12">
                    <input class="form-check-input confirm-participation" name="confirm-participation[]" type="checkbox" value="I agree to formally confirm my participation in the selected program(s)" id="confirm-participation-programs" <?php echo (!empty($existing_data) && $existing_data->confirm_participation == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="confirm-participation-programs">
                        I agree to formally confirm my participation in the selected program(s)
                    </label>
                </div>
                <div class="form-check col-12">
                    <input class="form-check-input" type="checkbox" name="confirm-participation[]" value="I agree to have my information shared with potential matchmaking partners." id="confirm-participation-share-info" <?php echo (!empty($existing_data) && $existing_data->share_with_partners == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="confirm-participation-share-info">
                        I agree to have my information shared with potential matchmaking partners.
                    </label>
                </div>
                <div class="form-check col-12">
                    <input class="form-check-input" type="checkbox" name="confirm-participation[]" value="I consent to be contacted after the event for feedback and future opportunities." id="confirm-participation-contact" <?php echo (!empty($existing_data) && $existing_data->post_event_contact == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="confirm-participation-contact">
                        I consent to be contacted after the event for feedback and future opportunities.
                    </label>
                </div>
                <div class="form-check col-12">
                    <input class="form-check-input" type="checkbox" name="confirm-participation[]" value="I agree to the terms and conditions / code of conduct (on goglobalconference.com) of this event." id="confirm-participation-terms" <?php echo (!empty($existing_data) && $existing_data->agree_terms == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="confirm-participation-terms">
                        I agree to the terms and conditions / code of conduct (on goglobalconference.com) of this event.
                    </label>
                </div>
                <div class="form-check col-12">
                    <input class="form-check-input" type="checkbox" name="disagree_terms" value="1" id="defaultCheck1" <?php echo (!empty($existing_data) && $existing_data->disagree_terms == 1) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="defaultCheck1">
                        I do not agree.
                    </label>
                </div>
            </div>
        </div>
        <div class="row g-3 text-center mt-4 mb-4">
            <div class="col-12">
                <button type="submit" class="theme-btn">Submit Registration <i class="far fa-paper-plane"></i></button>
            </div>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('dayForm');

        form.addEventListener('submit', function(event) {
            // Let the existing validation run first
            setTimeout(() => {
                // Check if form validation failed (Bootstrap adds was-validated class)
                if (form.classList.contains('was-validated') && !form.checkValidity()) {
                    // Find the first invalid field
                    const firstInvalidField = form.querySelector(':invalid');
                    if (firstInvalidField) {
                        // Calculate scroll position with offset for header
                        const rect = firstInvalidField.getBoundingClientRect();
                        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                        const offset = 200; // Adjust this value based on your header height
                        const targetPosition = rect.top + scrollTop - offset;

                        // Scroll to the field smoothly
                        window.scrollTo({
                            top: Math.max(0, targetPosition),
                            behavior: 'smooth'
                        });

                        // Focus on the field after scrolling
                        setTimeout(() => {
                            firstInvalidField.focus();
                        }, 600);
                    }
                }
            }, 100); // Small delay to let existing validation complete
        });
    });
</script>