<?php

// Theme setup
function mytheme_setup()
{
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ));
    register_nav_menu('main-menu', __('Main Menu'));
}
add_action('after_setup_theme', 'mytheme_setup');

// Enqueue styles and scripts
function mytheme_enqueue_scripts()
{
    // Styles
    wp_enqueue_style('bootstrap', get_template_directory_uri() . '/assets/css/bootstrap.min.css');
    wp_enqueue_style('fontawesome', get_template_directory_uri() . '/assets/css/all-fontawesome.min.css');
    wp_enqueue_style('animate', get_template_directory_uri() . '/assets/css/animate.min.css');
    wp_enqueue_style('magnific-popup', get_template_directory_uri() . '/assets/css/magnific-popup.min.css');
    wp_enqueue_style('owl-carousel', get_template_directory_uri() . '/assets/css/owl.carousel.min.css');
    wp_enqueue_style('template-style', get_template_directory_uri() . '/assets/css/style.css'); // Original template styles
    wp_enqueue_style('main-style', get_stylesheet_uri()); // WordPress theme overrides (load last)

    // Scripts
    wp_enqueue_script('jquery'); // WordPress's built-in jQuery
    wp_enqueue_script('modernizr', get_template_directory_uri() . '/assets/js/modernizr.min.js', [], null, true);
    wp_enqueue_script('bootstrap', get_template_directory_uri() . '/assets/js/bootstrap.bundle.min.js', ['jquery'], null, true);
    wp_enqueue_script('imagesloaded', get_template_directory_uri() . '/assets/js/imagesloaded.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('magnific-popup', get_template_directory_uri() . '/assets/js/jquery.magnific-popup.min.js', ['jquery'], null, true);
    wp_enqueue_script('isotope', get_template_directory_uri() . '/assets/js/isotope.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('appear', get_template_directory_uri() . '/assets/js/jquery.appear.min.js', ['jquery'], null, true);
    wp_enqueue_script('easing', get_template_directory_uri() . '/assets/js/jquery.easing.min.js', ['jquery'], null, true);
    wp_enqueue_script('owl-carousel', get_template_directory_uri() . '/assets/js/owl.carousel.min.js', ['jquery'], null, true);
    wp_enqueue_script('counter-up', get_template_directory_uri() . '/assets/js/counter-up.js', ['jquery'], null, true);
    wp_enqueue_script('wow', get_template_directory_uri() . '/assets/js/wow.min.js', ['jquery'], null, true);
    wp_enqueue_script('countdown', get_template_directory_uri() . '/assets/js/countdown.min.js', ['jquery'], null, true);
    wp_enqueue_script('main-js', get_template_directory_uri() . '/assets/js/main.js', ['jquery', 'owl-carousel'], null, true);

    // Enqueue contact form script on contact page
    if (is_page('contact-us') || is_page_template('page-contact-us.php')) {
        wp_enqueue_script('contact-form-js', get_template_directory_uri() . '/assets/js/contact-form.js', ['jquery'], '1.0', true);

        // Localize script for AJAX
        wp_localize_script('contact-form-js', 'contact_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('contact_form_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'mytheme_enqueue_scripts');

add_filter('body_class', 'add_home2_body_class');
function add_home2_body_class($classes)
{
    if (is_front_page()) {
        $classes[] = 'home-2';
    }
    return $classes;
}

// Register widget areas
function mytheme_widgets_init()
{
    // Footer Widget Area 1
    register_sidebar(array(
        'name'          => __('Footer Widget 1', 'mytheme'),
        'id'            => 'footer-widget-1',
        'description'   => __('First footer widget area (Newsletter/About)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box about-us">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Widget Area 2
    register_sidebar(array(
        'name'          => __('Footer Widget 2', 'mytheme'),
        'id'            => 'footer-widget-2',
        'description'   => __('Second footer widget area (Quick Links)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Widget Area 3
    register_sidebar(array(
        'name'          => __('Footer Widget 3', 'mytheme'),
        'id'            => 'footer-widget-3',
        'description'   => __('Third footer widget area (Social Media)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Widget Area 4
    register_sidebar(array(
        'name'          => __('Footer Widget 4', 'mytheme'),
        'id'            => 'footer-widget-4',
        'description'   => __('Fourth footer widget area (Contact Info)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Bottom Bar Widget Area
    register_sidebar(array(
        'name'          => __('Footer Bottom Bar', 'mytheme'),
        'id'            => 'footer-bottom-bar',
        'description'   => __('Footer bottom bar area (Copyright & Links)', 'mytheme'),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '',
        'after_title'   => '',
    ));

    // Header Top Bar Widget Area
    register_sidebar(array(
        'name'          => __('Header Top Bar', 'mytheme'),
        'id'            => 'header-top-bar',
        'description'   => __('Header top bar area (Contact & Social)', 'mytheme'),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '',
        'after_title'   => '',
    ));
}
add_action('widgets_init', 'mytheme_widgets_init');

// Include nav walker
require_once get_template_directory() . '/class-bootstrap-navwalker.php';

// Include custom widgets
require_once get_template_directory() . '/inc/custom-widgets.php';

// Include AJAX handlers
require_once get_template_directory() . '/template/register/add-participant-handler.php';

// Auto-setup footer widgets with default content (runs once)
function mytheme_setup_default_widgets()
{
    if (get_option('mytheme_widgets_setup') !== 'done') {

        // Setup Newsletter Widget
        $newsletter_widget = array(
            'description' => 'We are many variations of passages available majority have suffered in some injected content of a page when looking at its layout humour words believable.'
        );

        // Setup Quick Links Widget
        $links_widget = array(
            'title' => 'Quick Links',
            'link_text_1' => 'About Us',
            'link_url_1' => '#',
            'link_text_2' => 'Update News',
            'link_url_2' => '#',
            'link_text_3' => 'Contact Us',
            'link_url_3' => '#',
            'link_text_4' => 'Testimonials',
            'link_url_4' => '#',
            'link_text_5' => 'Terms Of Service',
            'link_url_5' => '#',
            'link_text_6' => 'Privacy Policy',
            'link_url_6' => '#'
        );

        // Setup Social Widget
        $social_widget = array(
            'title' => 'Our Social',
            'facebook' => 'https://facebook.com',
            'twitter' => 'https://twitter.com',
            'instagram' => 'https://instagram.com',
            'youtube' => 'https://youtube.com',
            'whatsapp' => 'https://whatsapp.com',
            'linkedin' => 'https://linkedin.com'
        );

        // Setup Contact Widget
        $contact_widget = array(
            'title' => 'Get In Touch',
            'phone' => '****** 654 7898',
            'address' => '25/B Milford Road, New York',
            'email' => '<EMAIL>',
            'button_text' => 'Buy Ticket',
            'button_url' => '#'
        );

        // Mark as setup complete
        update_option('mytheme_widgets_setup', 'done');
    }
}

add_action('init', function () {
    if (!session_id()) {
        session_start();
    }
});

add_action('after_switch_theme', 'mytheme_setup_default_widgets');
add_filter('show_admin_bar', '__return_false');

// Contact Form Handler
function handle_contact_form_submission() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['contact_nonce'], 'contact_form_nonce')) {
        wp_die('Security check failed');
    }



    // Sanitize and validate input data
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);

    // Validate required fields
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        wp_send_json_error('All fields are required.');
        return;
    }

    // Validate email
    if (!is_email($email)) {
        wp_send_json_error('Please enter a valid email address.');
        return;
    }

    // Basic spam protection - check for suspicious patterns
    if (is_spam_submission($name, $email, $message)) {
        wp_send_json_error('Your submission appears to be spam. Please try again.');
        return;
    }

    // Rate limiting - prevent too many submissions from same IP
    if (is_rate_limited($_SERVER['REMOTE_ADDR'])) {
        wp_send_json_error('Too many submissions. Please wait before submitting again.');
        return;
    }

    // Save to database
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Ensure table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
    if (!$table_exists) {
        create_contact_submissions_table();
    }

    $result = $wpdb->insert(
        $table_name,
        array(
            'name' => $name,
            'email' => $email,
            'subject' => $subject,
            'message' => $message,
            'submission_date' => current_time('mysql'),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ),
        array('%s', '%s', '%s', '%s', '%s', '%s', '%s')
    );

    if ($result === false) {
        error_log('Contact form database insert failed: ' . $wpdb->last_error);
        wp_send_json_error('There was an error saving your message. Please try again.');
        return;
    }

    // Send email notification
    $admin_email = get_option('admin_email');
    $site_name = get_bloginfo('name');

    $email_subject = sprintf('[%s] New Contact Form Submission: %s', $site_name, $subject);

    $email_message = sprintf(
        "New contact form submission received:\n\n" .
        "Name: %s\n" .
        "Email: %s\n" .
        "Subject: %s\n" .
        "Message:\n%s\n\n" .
        "Submitted on: %s\n" .
        "IP Address: %s",
        $name,
        $email,
        $subject,
        $message,
        current_time('mysql'),
        $_SERVER['REMOTE_ADDR']
    );

    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $site_name . ' <' . $admin_email . '>',
        'Reply-To: ' . $name . ' <' . $email . '>'
    );

    $mail_sent = wp_mail($admin_email, $email_subject, $email_message, $headers);

    if (!$mail_sent) {
        error_log('Contact form email failed to send');
    }

    // Send auto-reply email to user
    send_contact_form_auto_reply($name, $email, $subject);

    // Send success response
    wp_send_json_success('Thank you for your message! We will get back to you soon.');
}

// Send auto-reply email to user
function send_contact_form_auto_reply($name, $email, $subject) {
    $site_name = get_bloginfo('name');
    $admin_email = get_option('admin_email');

    $reply_subject = sprintf('Thank you for contacting %s', $site_name);

    $reply_message = sprintf(
        "Dear %s,\n\n" .
        "Thank you for reaching out to us regarding: %s\n\n" .
        "We have received your message and will get back to you as soon as possible. " .
        "Our team typically responds within 24-48 hours during business days.\n\n" .
        "If your inquiry is urgent, please feel free to call us at (877) 964-6222.\n\n" .
        "Best regards,\n" .
        "The %s Team\n\n" .
        "---\n" .
        "This is an automated response. Please do not reply to this email.",
        $name,
        $subject,
        $site_name
    );

    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $site_name . ' <' . $admin_email . '>'
    );

    $auto_reply_sent = wp_mail($email, $reply_subject, $reply_message, $headers);

    if (!$auto_reply_sent) {
        error_log('Contact form auto-reply email failed to send to: ' . $email);
    }

    return $auto_reply_sent;
}

// Basic spam detection
function is_spam_submission($name, $email, $message) {
    // Check for common spam patterns
    $spam_patterns = array(
        '/\b(viagra|cialis|casino|poker|loan|mortgage|insurance|pharmacy)\b/i',
        '/\b(click here|visit now|buy now|order now)\b/i',
        '/http[s]?:\/\/[^\s]{3,}/i', // Multiple URLs
        '/\b[A-Z]{10,}\b/', // Too many consecutive capitals
    );

    $content = $name . ' ' . $email . ' ' . $message;

    foreach ($spam_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            return true;
        }
    }

    // Check for too many links
    if (substr_count($message, 'http') > 2) {
        return true;
    }

    // Check message length (too short or too long)
    if (strlen($message) < 10 || strlen($message) > 5000) {
        return true;
    }

    return false;
}

// Rate limiting function
function is_rate_limited($ip_address) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Check submissions from this IP in the last hour
    $recent_submissions = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name
         WHERE ip_address = %s
         AND submission_date > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
        $ip_address
    ));

    // Allow maximum 3 submissions per hour per IP
    return $recent_submissions >= 3;
}

// Hook for logged-in users
add_action('wp_ajax_submit_contact_form', 'handle_contact_form_submission');
// Hook for non-logged-in users
add_action('wp_ajax_nopriv_submit_contact_form', 'handle_contact_form_submission');

// Create contact submissions table
function create_contact_submissions_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'contact_submissions';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        name tinytext NOT NULL,
        email varchar(100) NOT NULL,
        subject tinytext NOT NULL,
        message text NOT NULL,
        submission_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        status varchar(20) DEFAULT 'unread' NOT NULL,
        admin_notes text,
        PRIMARY KEY (id),
        KEY submission_date (submission_date),
        KEY status (status)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Update version option to track database changes
    update_option('contact_submissions_db_version', '1.0');
}

// Hook to create table on theme activation
add_action('after_switch_theme', 'create_contact_submissions_table');

// Also create table on init if it doesn't exist (backup)
add_action('init', 'ensure_contact_table_exists');

function ensure_contact_table_exists() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Check if table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
    if (!$table_exists) {
        create_contact_submissions_table();
        error_log('Contact submissions table created on init');
    }
}

// Add admin menu for contact submissions
function add_contact_submissions_admin_menu() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Get count of unread submissions
    $unread_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE status = 'unread'");

    $menu_title = 'Contact Forms';
    if ($unread_count > 0) {
        $menu_title .= ' <span class="awaiting-mod">' . $unread_count . '</span>';
    }

    add_menu_page(
        'Contact Submissions',
        $menu_title,
        'manage_options',
        'contact-submissions',
        'display_contact_submissions_page',
        'dashicons-email-alt',
        30
    );
}
add_action('admin_menu', 'add_contact_submissions_admin_menu');

// Display contact submissions admin page
function display_contact_submissions_page() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Handle status updates
    if (isset($_POST['update_status']) && isset($_POST['submission_id']) && isset($_POST['new_status'])) {
        $submission_id = intval($_POST['submission_id']);
        $new_status = sanitize_text_field($_POST['new_status']);
        $admin_notes = sanitize_textarea_field($_POST['admin_notes']);

        $wpdb->update(
            $table_name,
            array(
                'status' => $new_status,
                'admin_notes' => $admin_notes
            ),
            array('id' => $submission_id),
            array('%s', '%s'),
            array('%d')
        );

        echo '<div class="notice notice-success"><p>Submission updated successfully!</p></div>';
    }

    // Handle bulk delete
    if (isset($_POST['bulk_action']) && $_POST['bulk_action'] === 'delete' && isset($_POST['submission_ids'])) {
        $submission_ids = array_map('intval', $_POST['submission_ids']);
        $placeholders = implode(',', array_fill(0, count($submission_ids), '%d'));
        $wpdb->query($wpdb->prepare("DELETE FROM $table_name WHERE id IN ($placeholders)", $submission_ids));

        echo '<div class="notice notice-success"><p>Selected submissions deleted successfully!</p></div>';
    }

    // Get submissions with pagination
    $per_page = 20;
    $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $offset = ($current_page - 1) * $per_page;

    $total_submissions = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    $submissions = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name ORDER BY submission_date DESC LIMIT %d OFFSET %d",
        $per_page,
        $offset
    ));

    $total_pages = ceil($total_submissions / $per_page);

    ?>
    <div class="wrap">
        <h1>Contact Form Submissions</h1>

        <div class="tablenav top">
            <div class="alignleft actions">
                <form method="post" style="display: inline;">
                    <select name="bulk_action">
                        <option value="">Bulk Actions</option>
                        <option value="delete">Delete</option>
                    </select>
                    <input type="submit" class="button" value="Apply">
                </form>
            </div>

            <div class="tablenav-pages">
                <span class="displaying-num"><?php echo $total_submissions; ?> items</span>
                <?php if ($total_pages > 1): ?>
                    <span class="pagination-links">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a class="<?php echo $i === $current_page ? 'current' : ''; ?>"
                               href="?page=contact-submissions&paged=<?php echo $i; ?>"><?php echo $i; ?></a>
                        <?php endfor; ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all">
                    </td>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Subject</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($submissions)): ?>
                    <tr>
                        <td colspan="7">No contact submissions found.</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($submissions as $submission): ?>
                        <tr>
                            <th class="check-column">
                                <input type="checkbox" name="submission_ids[]" value="<?php echo $submission->id; ?>">
                            </th>
                            <td><?php echo esc_html($submission->name); ?></td>
                            <td><a href="mailto:<?php echo esc_attr($submission->email); ?>"><?php echo esc_html($submission->email); ?></a></td>
                            <td><?php echo esc_html($submission->subject); ?></td>
                            <td><?php echo date('M j, Y g:i A', strtotime($submission->submission_date)); ?></td>
                            <td>
                                <span class="status-<?php echo esc_attr($submission->status); ?>">
                                    <?php echo ucfirst(esc_html($submission->status)); ?>
                                </span>
                            </td>
                            <td>
                                <a href="#" class="view-submission" data-id="<?php echo $submission->id; ?>">View</a> |
                                <a href="#" class="edit-submission" data-id="<?php echo $submission->id; ?>">Edit</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Modal for viewing submission details -->
    <div id="submission-modal" class="submission-modal" style="display: none;">
        <div class="submission-modal-content">
            <div class="submission-modal-header">
                <h2>Contact Submission Details</h2>
                <span class="submission-modal-close">&times;</span>
            </div>
            <div class="submission-modal-body">
                <div id="submission-details"></div>
                <form id="update-submission-form" method="post">
                    <input type="hidden" name="submission_id" id="modal-submission-id">
                    <table class="form-table">
                        <tr>
                            <th><label for="modal-status">Status:</label></th>
                            <td>
                                <select name="new_status" id="modal-status">
                                    <option value="unread">Unread</option>
                                    <option value="read">Read</option>
                                    <option value="replied">Replied</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="modal-admin-notes">Admin Notes:</label></th>
                            <td>
                                <textarea name="admin_notes" id="modal-admin-notes" rows="4" cols="50" placeholder="Add internal notes about this submission..."></textarea>
                            </td>
                        </tr>
                    </table>
                    <p class="submit">
                        <input type="submit" name="update_status" class="button-primary" value="Update Submission">
                        <button type="button" class="button" onclick="replyToSubmission()">Reply via Email</button>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <style>
        .status-unread { color: #d63638; font-weight: bold; }
        .status-read { color: #00a32a; }
        .status-replied { color: #0073aa; }
        .submission-details { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #0073aa; }

        /* Modal Styles */
        .submission-modal {
            position: fixed;
            z-index: 100000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .submission-modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: 1px solid #888;
            width: 80%;
            max-width: 800px;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .submission-modal-header {
            padding: 20px;
            background-color: #f1f1f1;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .submission-modal-header h2 {
            margin: 0;
        }

        .submission-modal-close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .submission-modal-close:hover {
            color: #000;
        }

        .submission-modal-body {
            padding: 20px;
        }

        .submission-info {
            background: #f9f9f9;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #0073aa;
        }

        .submission-info h3 {
            margin-top: 0;
        }

        .submission-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .submission-message {
            background: #fff;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Bulk select functionality
        $('#cb-select-all').on('change', function() {
            $('input[name="submission_ids[]"]').prop('checked', this.checked);
        });

        // View submission modal
        $('.view-submission').on('click', function(e) {
            e.preventDefault();
            var submissionId = $(this).data('id');
            loadSubmissionDetails(submissionId);
        });

        // Edit submission (same as view but with focus on form)
        $('.edit-submission').on('click', function(e) {
            e.preventDefault();
            var submissionId = $(this).data('id');
            loadSubmissionDetails(submissionId, true);
        });

        // Close modal
        $('.submission-modal-close').on('click', function() {
            $('#submission-modal').hide();
        });

        // Close modal when clicking outside
        $(window).on('click', function(e) {
            if (e.target.id === 'submission-modal') {
                $('#submission-modal').hide();
            }
        });

        // Load submission details via AJAX
        function loadSubmissionDetails(submissionId, focusEdit = false) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_submission_details',
                    submission_id: submissionId,
                    nonce: '<?php echo wp_create_nonce('contact_admin_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        displaySubmissionModal(response.data, focusEdit);
                    } else {
                        alert('Error loading submission details: ' + response.data);
                    }
                },
                error: function() {
                    alert('Error loading submission details');
                }
            });
        }

        function displaySubmissionModal(submission, focusEdit = false) {
            var detailsHtml = `
                <div class="submission-info">
                    <h3>Contact Submission #${submission.id}</h3>
                    <div class="submission-meta">
                        <div><strong>Name:</strong> ${submission.name}</div>
                        <div><strong>Email:</strong> <a href="mailto:${submission.email}">${submission.email}</a></div>
                        <div><strong>Subject:</strong> ${submission.subject}</div>
                        <div><strong>Date:</strong> ${submission.submission_date}</div>
                        <div><strong>IP Address:</strong> ${submission.ip_address}</div>
                        <div><strong>Status:</strong> <span class="status-${submission.status}">${submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}</span></div>
                    </div>
                    <div>
                        <strong>Message:</strong>
                        <div class="submission-message">${submission.message}</div>
                    </div>
                    ${submission.admin_notes ? '<div><strong>Admin Notes:</strong><div class="submission-message">' + submission.admin_notes + '</div></div>' : ''}
                </div>
            `;

            $('#submission-details').html(detailsHtml);
            $('#modal-submission-id').val(submission.id);
            $('#modal-status').val(submission.status);
            $('#modal-admin-notes').val(submission.admin_notes || '');

            $('#submission-modal').show();

            if (focusEdit) {
                $('#modal-status').focus();
            }
        }
    });

    function replyToSubmission() {
        var email = $('#submission-details').find('a[href^="mailto:"]').text();
        var subject = $('#submission-details').find('strong:contains("Subject:")').parent().text().replace('Subject: ', '');
        var replySubject = 'Re: ' + subject;

        var mailtoLink = 'mailto:' + email + '?subject=' + encodeURIComponent(replySubject);
        window.open(mailtoLink);

        // Optionally update status to "replied"
        $('#modal-status').val('replied');
    }
    </script>
    <?php
}

// AJAX handler for getting submission details
function get_submission_details() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'contact_admin_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $submission_id = intval($_POST['submission_id']);

    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    $submission = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE id = %d",
        $submission_id
    ));

    if (!$submission) {
        wp_send_json_error('Submission not found');
        return;
    }

    // Mark as read if it was unread
    if ($submission->status === 'unread') {
        $wpdb->update(
            $table_name,
            array('status' => 'read'),
            array('id' => $submission_id),
            array('%s'),
            array('%d')
        );
        $submission->status = 'read';
    }

    // Format the submission data
    $formatted_submission = array(
        'id' => $submission->id,
        'name' => esc_html($submission->name),
        'email' => esc_html($submission->email),
        'subject' => esc_html($submission->subject),
        'message' => esc_html($submission->message),
        'submission_date' => date('M j, Y g:i A', strtotime($submission->submission_date)),
        'ip_address' => esc_html($submission->ip_address),
        'status' => esc_html($submission->status),
        'admin_notes' => esc_html($submission->admin_notes)
    );

    wp_send_json_success($formatted_submission);
}

// Hook the AJAX handler
add_action('wp_ajax_get_submission_details', 'get_submission_details');
